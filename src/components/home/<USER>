// @ts-nocheck
import React from 'react';
import React, { useState } from "react";
import Box from "@amzn/awsui-components-react/polaris/box";
import <PERSON>Field from "@amzn/awsui-components-react/polaris/form-field";
import SpaceBetween from "@amzn/awsui-components-react/polaris/space-between";
import Container from "@amzn/awsui-components-react/polaris/container";
import Header from "@amzn/awsui-components-react/polaris/header";
import Toggle from "@amzn/awsui-components-react/polaris/toggle";
import Select from "@amzn/awsui-components-react/polaris/select";
import Input from "@amzn/awsui-components-react/polaris/input";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { useStore } from "../../data/store";
import IconSelectElement from "./IconSelectElement";

interface Input {
  id: string;
  label: string;
  value: string;
  maxLen: number;
  rte?: boolean;
  dropdown?: [string, string][];
  customUrl?: boolean;
  theme?: boolean;
  type?: string;
  date?: boolean;
}

interface CardElementProps {
  element: {
    label: string;
    copy: number;
    selected: boolean;
    inputs: Input[];
  };
  index: number;
}

const toolbarOptions = [
  [{ size: ["small", false, "large"] }],
  ["bold", "italic", "underline"],
  ["link"],
  ["clean"],
];

const modules = {
  toolbar: toolbarOptions,
  clipboard: {
    matchVisual: true,
  },
};

const formats = [
  "header",
  "bold",
  "italic",
  "underline",
  "strike",
  "list",
  "bullet",
  "indent",
  "link",
  "image",
  "size",
];

const CardElement: React.FC<CardElementProps> = ({ element, index }) => {
  const toggleCard = useStore((state) => state.toggleElement);
  const editValue = useStore((state) => state.editValue);
  const editHighlightEl = useStore((state) => state.editHighlightEl);
  const [cardActive, setCardActive] = useState(false);
  const [rteLen, setRteLen] = useState(0);

  function highlightModule(e: React.MouseEvent<HTMLDivElement>) {
    if (!cardActive) editHighlightEl("");
    editHighlightEl(e.currentTarget.id);
  }

  return (
    <Box>
      <Container
        variant="default"
        id={element.label + element.copy}
        onClick={highlightModule}
      >
        <Header
          actions={
            <Toggle
              checked={element.selected}
              onChange={({ detail }) => {
                const isCardSelected = detail.checked;

                if (isCardSelected && cardActive) {
                  setCardActive(true);
                } else if (!isCardSelected && !cardActive) {
                  setCardActive(false);
                } else {
                  setCardActive(!cardActive);
                }
                toggleCard(index);
              }}
            />
          }
        >
          <SpaceBetween direction="horizontal" size="xs">
            <span>{element.label} Module</span>
            {element.copy > 0 && `(Copy ${element.copy})`}
          </SpaceBetween>
        </Header>

        {cardActive && (
          <SpaceBetween size="l">
            {element.inputs.map((input, i) => (
              <Box key={input.id}>
                <FormField
                  label={input.label.replace("_", " ")}
                >
                  {input.rte && (
                    <SpaceBetween size="xs">
                      <ReactQuill
                        theme="snow"
                        modules={modules}
                        formats={formats}
                        value={input.value}
                        onChange={(value, delta, source, editor) => {
                          const charCount = editor.getLength();
                          editValue(index, input.id, value);
                          setRteLen(charCount - 1);
                        }}
                        style={{
                          backgroundColor: 'white',
                          borderRadius: '4px',
                          border: '2px solid #aab7b8'
                        }}
                      />
                      <span
                        style={{
                          fontSize: '12px',
                          color: rteLen > input.maxLen ? '#d91515' : '#000000'
                        }}
                      >
                        {rteLen}/{input.maxLen} recommended character count
                      </span>
                    </SpaceBetween>
                  )}

                  {input.dropdown && (
                    <SpaceBetween size="xs">
                      <Select
                        selectedOption={null}
                        onChange={({ detail }) => {
                          if (detail.selectedOption.value === "") {
                            editValue(index, input.id, true, "customUrl");
                          } else {
                            editValue(index, input.id, false, "customUrl");
                            editValue(index, input.id, detail.selectedOption.value);
                          }
                        }}
                        options={[
                          ...input.dropdown.map(([label, value]) => ({
                            label,
                            value
                          })),
                          { label: "custom URL", value: "" }
                        ]}
                      />
                      {input.customUrl && (
                        <Input
                          type="text"
                          value={input.value}
                          onChange={({ detail }) => {
                            const URLregex = new RegExp(
                              "(https?://)?([\\da-z.-]+)\\.([a-z.]{2,6})[/\\w .-]*/?");
                            if (detail.value && URLregex.test(detail.value)) {
                              editValue(index, input.id, detail.value);
                            }
                          }}
                          maxLength={input.maxLen}
                        />
                      )}
                    </SpaceBetween>
                  )}

                  {input.theme && input.type && (
                    <IconSelectElement index={index} input={input} />
                  )}

                  {input.date && (
                    <Input
                      type="date"
                      value={input.value}
                      onChange={({ detail }) => {
                        editValue(index, input.id, detail.value);
                      }}
                    />
                  )}

                  {(input.label === "top_space" || input.label === "bottom_space") && (
                    <Select
                      selectedOption={{ value: input.value }}
                      onChange={({ detail }) => {
                        editValue(index, input.id, detail.selectedOption.value);
                      }}
                      options={[0, 10, 20, 30, 40, 50].map((value) => ({
                        label: String(value),
                        value: String(value)
                      }))}
                    />
                  )}
                </FormField>
              </Box>
            ))}
          </SpaceBetween>
        )}
      </Container>
    </Box>
  );
};

export default CardElement;
