// IconSelectElement.tsx
import React from "react";
import Select from "@amzn/awsui-components-react/polaris/select";
import SpaceBetween from "@amzn/awsui-components-react/polaris/space-between";
import { useStore } from "../../data/store";

interface IconSelectProps {
  input: {
    id: string;
    type?: string;
    theme?: string;
  };
  index: number;
}

const IconSelectElement: React.FC<IconSelectProps> = ({ input, index }) => {
  const editValue = useStore((state) => state.editValue);
  let type = input.type || "AI";
  let theme = input.theme || "t1";

  const themeDropdown = ["t0", "t1", "t2", "t3", "t4"];
  const typeDropdown = [
    "AI",
    "Certificate",
    "Bars",
    "Chat",
    "Cloud",
    "Data",
    "Graphs",
    "Innovation",
    "Launch",
    "Learning",
    "Security",
    "Sessions",
  ];

  const editIcon = (value: string, key: "type" | "theme") => {
    if (key === "type") type = value;
    else if (key === "theme") theme = value;
    let url = `https://pages.awscloud.com/rs/112-TZM-766/images/aws-email-icon-${type}-${theme}.png`;

    editValue(index, input.id, url);
    editValue(index, input.id, value, key);
  };

  return (
    <SpaceBetween direction="horizontal" size="m">
      <Select
        selectedOption={{ value: type }}
        onChange={({ detail }) => {
          if (detail.selectedOption.value) {
            editIcon(detail.selectedOption.value, "type");
          }
        }}
        options={typeDropdown.map((item) => ({
          label: item,
          value: item
        }))}
      />
      <Select
        selectedOption={{ value: theme }}
        onChange={({ detail }) => {
          if (detail.selectedOption.value) {
            editIcon(detail.selectedOption.value, "theme");
          }
        }}
        options={themeDropdown.map((item, index) => ({
          label: `Theme ${index + 1}`,
          value: item
        }))}
      />
    </SpaceBetween>
  );
};

export default IconSelectElement;
