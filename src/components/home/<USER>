// @ts-nocheck
import React, { useEffect, useState, useRef } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from '@dnd-kit/utilities';
import { useStore } from "../../data/store";
import ExpandableSection from "@amzn/awsui-components-react/polaris/expandable-section";
import Button from "@amzn/awsui-components-react/polaris/button";
import Link from "@amzn/awsui-components-react/polaris/link";
import Toggle from "@amzn/awsui-components-react/polaris/toggle";
import Icon from "@amzn/awsui-components-react/polaris/icon";
import Box from '@amzn/awsui-components-react/polaris/box';
import Select from "@amzn/awsui-components-react/polaris/select";
import Input from '@amzn/awsui-components-react/polaris/input';
import SpaceBetween from '@amzn/awsui-components-react/polaris/space-between';
import TimeInput from "@amzn/awsui-components-react/polaris/time-input";
import DatePicker from "@amzn/awsui-components-react/polaris/date-picker";
import <PERSON>Field from "@amzn/awsui-components-react/polaris/form-field";
import AttributeEditor from "@amzn/awsui-components-react/polaris/attribute-editor";
import ReactQuill from "react-quill-new";
import 'react-quill-new/dist/quill.snow.css';
import { nanoid } from "nanoid";


const toolbarOptions = [
  // [{ font: ["Lato,sans-serif"] }],
  [{ size: ["small", false, "large"] }],

  ['bold', 'italic', 'underline'],
  [{ 'list': 'bullet' }, { 'list': 'ordered' }],
  ['link'],
  ['clean']
];
const modules = {
  toolbar: toolbarOptions,
  clipboard: {
    matchVisual: false, // Disable visual matching
  },
};
const formats = [
  "header",
  "bold",
  "italic",
  "underline",
  "strike",
  "list",
  "bullet",
  "indent",
  "link",
  "image",
  "size",

];
const spacingInPixels = [0, 10, 20, 30, 40, 50].map(num => ({
  value: num,
  label: num.toString()
}));

const ButtonElement: React.FC<ButtonElementProps> = (props) => {
  const { module, onDelete } = props;
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id: module.id });

  // Force re-render when module changes
  const [key, setKey] = useState(0);

  // Update key when module changes to force re-render
  useEffect(() => {
    setKey(prevKey => prevKey + 1);
  }, [module]);

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const elements = useStore(state => state.elements);
  const toggleModule = useStore(state => state.toggleElement);
  const editValue = useStore(state => state.editValue);
  const metadata = useStore(state => state.metadata);
  const updatedModule = elements.find(el => el.id === module.id);
  const [isExpanded, setIsExpanded] = useState(false);
  const [rteLen, setRteLen] = useState(0);
  const [cursorPosition, setCursorPosition] = useState<number | null>(null);


  const keyValuePairLabels = {
    "Event Agenda": ["time", "sessionName"],
    "Highlights": ["title", "description"]
  }

  const keyValuePairModule = module.inputs.find(inp => "keyValuePair" in inp && inp["keyValuePair"] === true)
  // console.log(agenda)
  const [keyValueItems, setKeyValueItems] = useState(keyValuePairModule ? keyValuePairModule.value : []);
  let keyValueInputId = ""

  useEffect(() => {

    // console.log("AGENDA", AgendaItems)
    if (keyValueItems.length > 0) editValue(module.id, keyValueInputId, [...keyValueItems])


  }, [keyValueItems]);

  const inputRef = useRef<HTMLInputElement>(null);

  return (
    // <button ref={setNodeRef} style={style} {...attributes} {...listeners}>{module.label}</button>
    <div id="module-inputs" ref={setNodeRef} style={style} {...attributes} {...listeners} >
      <ExpandableSection
        variant="container"
        defaultExpanded={false}
        expanded={isExpanded}
        onChange={({ detail }) => {
          setIsExpanded(detail.expanded);
        }}

        headerActions={<Button iconName="remove" variant="icon" className="delete-module-icon" onClick={() => props.onDelete?.({ id: module.id, title: module.label })}></Button>}
        headerInfo={<Toggle
          onChange={({ detail }) => {
            toggleModule(module.id)
            setIsExpanded(detail.checked)
          }
          }
          checked={updatedModule?.selected || false}
        >
        </Toggle>}
        headerCounter={' ' + (module.copy > 0 ? `(Copy ${module.copy})` : "")}
        headerText={module.label}
      // header={<div>module.label</div>}
      >
        <div style={{ touchAction: 'none' }}
          onPointerDown={(e) => e.stopPropagation()}
          onMouseDown={(e) => e.stopPropagation()}
          onClick={(e) => e.stopPropagation()}
          onDragStart={(e) => { e.preventDefault(); }}>
          <SpaceBetween size="m" >

            {module.inputs.map(input => {
              // Always get the latest value from the input
              const [inputValue, setInputValue] = useState(input.value);

              // Update local state when input.value changes
              useEffect(() => {
                setInputValue(input.value);
              }, [input.value]);

              // Update the store when local state changes
              const handleValueChange = (newValue) => {
                setInputValue(newValue);
                editValue(module.id, input.id, newValue);
              };

              if (input["keyValuePair"] === true) keyValueInputId = input.id
              return (
                <Box key={`${module.id}-${input.id}`}>
                  <Box>{input.label.split("_").join(" ")}</Box>
                  {input.label === "top_space" || input.label === "bottom_space" ? (
                    <Select
                      selectedOption={{ value: inputValue, label: inputValue.toString() }}
                      onChange={({ detail }) => handleValueChange(detail.selectedOption.value)}
                      options={spacingInPixels}
                    />
                  ) : input["rte"] === true ? (
                    <ReactQuill
                      theme="snow"
                      modules={modules}
                      formats={formats}
                      value={inputValue} // Use local state
                      onChange={(value) => handleValueChange(value)}
                    />
                  ) : input["dropdown"] ? (
                    <>
                      <Select
                        selectedOption={inputValue}
                        onChange={({ detail }) => {
                          if (detail.selectedOption.label === "Custom URL") {
                            handleValueChange(true, "customUrl")
                          }
                          else {
                            handleValueChange(false, "customUrl")
                          }
                          handleValueChange(detail.selectedOption)
                        }}
                        options={input.dropdown}
                      />
                      {inputValue === true && <Input
                        onChange={({ detail }) => handleValueChange({ label: "Custom URL", value: detail.value })}
                        value={input.value.value}
                        spellcheck
                      />}
                    </>) : input["time"] === true ? (
                      <TimeInput
                        onChange={({ detail }) => handleValueChange(detail.value)}
                        value={inputValue}
                        format="hh:mm"
                      />
                    ) : input["date"] ? (
                      <FormField
                        label=""
                        constraintText="Use YYYY/MM/DD format."
                      >
                        <DatePicker
                          onChange={({ detail }) => handleValueChange(detail.value)}
                          value={inputValue}
                          openCalendarAriaLabel={selectedDate =>
                            "Choose certificate expiry date" +
                            (selectedDate
                              ? `, selected date is ${selectedDate}`
                              : "")
                          }
                          placeholder="YYYY/MM/DD"
                        />
                      </FormField>
                    ) : input["keyValuePair"] === true ? (
                      <AttributeEditor
                        onAddButtonClick={() => setKeyValueItems([...keyValueItems, { id: nanoid(8), [keyValuePairLabels[module.label][0]]: "", [keyValuePairLabels[module.label][1]]: "", }])}
                        onRemoveButtonClick={({
                          detail: { itemIndex }
                        }) => {
                          const tmpItems = [...keyValueItems];
                          tmpItems.splice(itemIndex, 1);
                          setKeyValueItems(tmpItems)
                        }}
                        items={keyValueItems}
                        addButtonText="Add New Item"
                        definition={[
                          {
                            label: "",
                            control: (item, index) => (
                              <FormField
                                label={"Enter " + Object.keys(item)[1] + " " + (index + 1)}
                              >
                                <Input
                                  onChange={({ detail }) => {
                                    const newKeyValueItems = keyValueItems.map(keyValue =>
                                      keyValue.id === item.id
                                        ? {
                                          ...keyValue,
                                          [Object.keys(item)[1]]: detail.value
                                        }
                                        : keyValue
                                    );
                                    setKeyValueItems(newKeyValueItems);
                                  }}
                                  value={Object.values(item)[1]}
                                  placeholder={"Enter " + Object.keys(item)[1]}
                                />
                              </FormField>
                            )
                          },
                          {
                            label: "",
                            control: (item, index) => (
                              <FormField
                                label={"Enter " + Object.keys(item)[2] + " " + (index + 1)}
                              >
                                {Object.keys(item)[2] === "description" ? (
                                  <ReactQuill
                                    theme="snow"
                                    defaultValue={Object.values(item)[2]}
                                    onChange={(value) => {
                                      const newKeyValueItems = keyValueItems.map(keyValue =>
                                        keyValue.id === item.id
                                          ? {
                                            ...keyValue,
                                            [Object.keys(item)[2]]: value
                                          }
                                          : keyValue
                                      );
                                      setKeyValueItems(newKeyValueItems);
                                    }}
                                  />
                                ) : (
                                  <Input
                                    value={Object.values(item)[2]}
                                    placeholder="Enter session name"
                                    onChange={({ detail }) => {
                                      const newKeyValueItems = keyValueItems.map(keyValue =>
                                        keyValue.id === item.id
                                          ? {
                                            ...keyValue,
                                            [Object.keys(item)[2]]: detail.value
                                          }
                                          : keyValue
                                      );
                                      setKeyValueItems(newKeyValueItems);
                                    }}
                                  />
                                )}
                              </FormField>
                            )
                          }
                        ]}
                        empty="No items in this module, please add new modules"
                      />
                    ) : (
                    <Input
                      onChange={({ detail }) => handleValueChange(detail.value)}
                      value={inputValue} // Use local state
                      spellcheck
                    />
                  )}
                </Box>
              );
            })}
          </SpaceBetween>
        </div>
      </ExpandableSection>
    </div>
  );
};

export default ButtonElement;
