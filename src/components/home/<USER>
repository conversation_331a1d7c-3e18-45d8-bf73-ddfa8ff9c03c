// @ts-nocheck
import React from 'react';
import Box from '@amzn/awsui-components-react/polaris/box';
import Container from '@amzn/awsui-components-react/polaris/container';
import Input from '@amzn/awsui-components-react/polaris/input';
import Header from '@amzn/awsui-components-react/polaris/header';
import SpaceBetween from '@amzn/awsui-components-react/polaris/space-between';
import ColumnLayout from '@amzn/awsui-components-react/polaris/column-layout';
import Grid from "@amzn/awsui-components-react/polaris/grid";
import { useStore } from "../../data/store";
import LeftPanel from './LeftPanel';
import RightPanel from './RightPanel';

// Constants
const STYLE_CONSTANTS = {
  DOT_INDICATOR: {
    height: "16px",
    width: "16px",
    borderRadius: "50%",
    backgroundColor: "#0972d3"
  },
  EMAIL_PREVIEW: {
    borderTop: "2px solid #e9ebed",
    borderBottom: "2px solid #e9ebed",
    minWidth: "500px",
    maxWidth: "600px"
  }
};

interface MetadataInputProps {
  label: string;
  value: string;
  maxLength?: number;
  onChange: (value: string) => void;
}

const MetadataInput: React.FC<MetadataInputProps> = ({ label, value, maxLength, onChange }) => (
  <Box>
    <SpaceBetween size="xs">
      <Box variant="p" color="text-body-secondary">
        {label}
      </Box>
      <Input
        value={value}
        onChange={({ detail }) => { // Only allow input up to maxLength
          // if (detail.value.length <= maxLength) {
          onChange(detail.value);
          // }
        }}
      />
      <small style={{
        color: value.length > (maxLength || 0) ? "red" : "black",
        display: "block",
        textAlign: "right"
      }} className="characters-info">
        {value.length}/{maxLength || 0} recommended
        character count
      </small>
    </SpaceBetween>
  </Box>
);

interface EmailPreviewProps {
  metadata: {
    sender: string;
    subject: { value: string };
    preheader: { value: string };
  };
}

const EmailPreview: React.FC<EmailPreviewProps> = ({ metadata }) => (
  <Box margin={{ left: "xl" }}>
    <Box
      // margin={{ left: "xxl" }}
      variant='h1'
    >Inbox </Box>
    <Box >
      <SpaceBetween direction="horizontal" >
        <Box style={STYLE_CONSTANTS.DOT_INDICATOR} />
        <Box style={STYLE_CONSTANTS.EMAIL_PREVIEW}>
          <SpaceBetween direction="horizontal" size="m">
            <Box>
              <SpaceBetween size="xs">
                <Box fontSize="heading-xl" className='metadata-sender'><span>{metadata.sender}</span> <span>15:00</span></Box>
                <Box fontSize="heading-l">{metadata.subject.value}</Box>
                <Box color="text-body-secondary">{metadata.preheader.value}</Box>
              </SpaceBetween>
            </Box>
            <Box fontSize="heading-l"></Box>
          </SpaceBetween>
        </Box>
      </SpaceBetween>
    </Box>
  </Box>
);

export default function HomepageContent({ isCurrentUserOwner }) {
  const user = window.harmony.api.getUser();
  const metadata = useStore(state => state.metadata);
  const editMetadata = useStore(state => state.editMetadata);

  // const metadata = {}
  // const editMetadata = {}
  const renderMetadataInputs = () => (
    <Box>
      <SpaceBetween size="xs">
        <MetadataInput
          label={metadata.subject.label}
          value={metadata.subject.value}
          maxLength={metadata.subject.maxLen}
          onChange={(value) => editMetadata("subject", value)}
        />
        <MetadataInput
          label={metadata.preheader.label}
          value={metadata.preheader.value}
          maxLength={metadata.preheader.maxLen}
          onChange={(value) => editMetadata("preheader", value)}
        />
      </SpaceBetween>
    </Box>
  );

  return (
    <Container >
      <div style={{ maxWidth: '1280px', margin: '0 auto', width: '100%', backgroundColor: "#f9f9fa" }}>
        <Container
          variant="default"

          header={

            <Grid gridDefinition={[
              { colspan: { default: 5, xxs: 5 } },
              { colspan: { default: 7, xxs: 7 } }
            ]}>
              {renderMetadataInputs()}
              <EmailPreview metadata={metadata} />
            </Grid>

          }
        >
          <div >
            <Grid gridDefinition={[
              { colspan: { default: 5, xxs: 5 } },
              { colspan: { default: 7, xxs: 7 } }
            ]}  >
              {/* Additional components can go here */}
              <LeftPanel />
              <RightPanel isCurrentUserOwner={isCurrentUserOwner} />
            </Grid>
          </div>
        </Container>
      </div>
    </Container>
  );
}
