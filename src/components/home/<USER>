import React from "react";
import { useStore } from "../../data/store";

interface ModuleElementProps {
  html: string | (() => string);
  id: string;
}

const ModuleElement: React.FC<ModuleElementProps> = ({ html, id }) => {
  const highlightElement = useStore((state) => state.highlightElement);
  // console.log(html);

  //const bounced = useDebounce(isFocused, 3000);
  // useEffect(() => {
  //   console.log(document.getElementById(`${id}_focus`));
  //   document.getElementById(
  //     `${id}_focus`
  //   ).style = `backgroundColor: "#FFFAF0",border: "4px dashed #FEEBC8",`;
  //   const timeout = setTimeout(() => {
  //     document.getElementById(`${id}_focus`).style = `none`;
  //   }, 3000);
  //   //if (timeout) clearTimeout(timeout);
  // }, [highlightElement]);

  return (<div className="mktoText">{html}</div>);
};

export default ModuleElement;
