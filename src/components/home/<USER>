// @ts-nocheck
import * as React from "react";
import Box from '@amzn/awsui-components-react/polaris/box';
import Header from '@amzn/awsui-components-react/polaris/header';
import Input from '@amzn/awsui-components-react/polaris/input';
import Select from '@amzn/awsui-components-react/polaris/select';
import SpaceBetween from '@amzn/awsui-components-react/polaris/space-between';
import FormField from "@amzn/awsui-components-react/polaris/form-field";
// import Board from '@amzn/awsui-board-components/board';
// import BoardItem from '@amzn/awsui-board-components/board-item';
import Button from '@amzn/awsui-components-react/polaris/button';
import Grid from "@amzn/awsui-components-react/polaris/grid";
import { useStore } from "../../data/store";
// import { BoardProps } from '@amzn/awsui-board-components/board';
import Toggle from "@amzn/awsui-components-react/polaris/toggle";
import Slider from "@amzn/awsui-components-react/polaris/slider";
import ButtonDropdown from "@amzn/awsui-components-react/polaris/button-dropdown";
import ExpandableSection from "@amzn/awsui-components-react/polaris/expandable-section";

import { nanoid } from "nanoid";
import { closestCenter, closestCorners, DndContext, DragEndEvent, DragOverlay, DragStartEvent, PointerSensor, TouchSensor, useSensors, useSensor } from "@dnd-kit/core"
import { SortableContext, useSortable, verticalListSortingStrategy, arrayMove } from "@dnd-kit/sortable"
import { useState, useEffect } from "react";
import { CSS } from "@dnd-kit/utilities"
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import Modal from "@amzn/awsui-components-react/polaris/modal";
import ButtonElement from "./ButtonElement";
/**
 * LeftPanel Component - Handles email module management using Board layout
 */
const LeftPanel = () => {
  const elements = useStore(state => state.elements);
  const optionalElements = useStore(state => state.optionalElements);
  const metadata = useStore(state => state.metadata);
  const editMetadata = useStore(state => state.editMetadata);
  const reorderState = useStore(state => state.reorderState);
  const addModule = useStore(state => state.addModule);
  const toggleElement = useStore(state => state.toggleElement);
  const editValue = useStore(state => state.editValue);
  const deleteModule = useStore((state) => state.deleteModule);

  const trkRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;


  // First, declare the state
  const [StoreModules, setStoreModules] = useState(elements);

  // Then use the useEffect
  useEffect(() => {
    // Create a proper deep copy that preserves functions
    const elementsCopy = elements.map(element => {
      // Create a new object with all properties from the original element
      const newElement = { ...element };

      // Specifically handle the html function to preserve it
      if (typeof element.html === 'function') {
        newElement.html = function () {
          return element.html.call(this);
        };
      }

      return newElement;
    });

    setStoreModules(elementsCopy);
  }, [elements]);

  // Add a separate effect to log StoreModules after it updates
  useEffect(() => {
    console.log("StoreModules updated left panel:", StoreModules);
  }, [StoreModules]);

  // const ElementsForBoardUseOnly = elements.map(module => { return { ...module } })
  const [boardItems, setBoardItems] = useState(elements);

  const [ModalShow, setModalShow] = useState(false)
  const [deleteModuleInfo, setDeleteModuleInfo] = useState({})

  const [isDragStarted, setIsDragStarted] = useState(false)

  const [isTrkValid, setTrkValid] = useState(trkRegex.test(metadata.trk.value))

  console.log(isTrkValid)

  const sensors = useSensors(
    useSensor(PointerSensor, {
      // For trackpad, a small activation distance helps prevent accidental drags
      activationConstraint: {
        distance: 5 // 5px movement before drag starts
      }
    })
  );

  // Add a new state to track expanded items using their IDs
  const [expandedItems, setExpandedItems] = useState(new Set());
  // Add a function to toggle expansion for individual items
  const toggleExpansion = (itemId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };
  const toolbarOptions = [
    // [{ font: ["Lato,sans-serif"] }],
    [{ size: ["small", false, "large"] }],

    ["bold", "italic", "underline"],
    ["link"],
    ["clean"],
  ];
  const modules = {
    toolbar: toolbarOptions,
    clipboard: {
      matchVisual: true, // Disable visual matching
    },
  };
  const formats = [
    "header",
    "bold",
    "italic",
    "underline",
    "strike",
    "list",
    "bullet",
    "indent",
    "link",
    "image",
    "size",
  ];

  // console.warn(elements)
  /**
   * Handles the reordering of elements
   * @param {Object} detail - Contains source and destination indices
   */
  const handleReorder = (items) => {
    console.log(items)
    // const { sourceIndex, targetIndex } = detail;

    const reorderedElements = [...items];
    // const [removedElement] = reorderedElements.splice(sourceIndex, 1);
    // reorderedElements.splice(targetIndex, 0, removedElement);
    reorderState(reorderedElements);
  };

  /**
   * Handles module addition
   * @param {Object} detail - Contains the selected option
   */
  const handleModuleAdd = ({ detail }) => {
    if (!detail.selectedOption.value) return;

    const selectedModule = { ...optionalElements[detail.selectedOption.value] };
    const modulesExists = elements.filter(
      (module) => module.label === selectedModule.label
    );

    selectedModule.copy = modulesExists.length;
    selectedModule.selected = true;
    selectedModule.id = nanoid(8),
      addModule(selectedModule);
    // console.warn(elements)
    const currentElements = useStore.getState().elements;
    setBoardItems([...currentElements]);
  };

  const handleDragEnd = (event) => {
    setIsDragStarted(false)
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setStoreModules((items) => {
        const oldIndex = items.findIndex(item => item.id === active.id);
        const newIndex = items.findIndex(item => item.id === over.id);

        const newOrder = arrayMove(items, oldIndex, newIndex);
        // Update the global state if needed
        // reorderState(newOrder);
        handleReorder(newOrder);
        console.log(newOrder);

        return newOrder;
      });

    };
  }

  const testModules = [
    { id: '1', label: 'Test Item 1' },
    { id: '2', label: 'Test Item 2' }
  ];

  const handleModuleDelete = (moduleInfo) => {
    // Handle the delete action here
    // const { id, title } = moduleInfo
    setDeleteModuleInfo(moduleInfo)
    setModalShow(true)

    // Add your delete logic here
  };
  return (
    <Box flex="2">
      <SpaceBetween size="l">
        <Header variant="h2">
          Email Inputs
        </Header>

        <Box

          backgroundColor="background.container"
          borderRadius="container"
          style={{ height: 'auto' }}
        >
          <SpaceBetween size="l">
            {/* TRK Code Input Section */}
            <Box>
              <SpaceBetween size="xs">
                <FormField

                  constraintText={
                    <>
                      {/* "TRK code must be 36 characters [ eg. 43a48e28-a6b3-4366-8a90-1a285434e09c ]" */}
                    </>
                  }
                >
                  <Input
                    placeholder="Enter TRK Code here [ eg. 43a48e28-a6b3-4366-8a90-1a285434e09c ]"
                    value={metadata.trk.value}
                    onChange={({ detail }) => {
                      if (trkRegex.test(detail.value)) {
                        setTrkValid(true)
                      } else setTrkValid(false)
                      editMetadata("trk", detail.value)
                    }}
                    maxLength={metadata.trk.maxLen}
                  />
                </FormField>
                {/* <Input
                  placeholder="Enter TRK Code here [ eg. 43a48e28-a6b3-4366-8a90-1a285434e09c ]"
                  value={metadata.trk.value}
                  onChange={({ detail }) => editMetadata("trk", detail.value)}
                  maxLength={metadata.trk.maxLen}
                /> */}
              </SpaceBetween>
            </Box>

            {/* Board with draggable items */}

            {/* <Board
              stickyHeader={false} //
              items={elements.map(element => ({
                ...element,
                // Set rowSpan and columnSpan here in the items array
                columnSpan: 12,
                minRowSpan: 1,
                defaultRowSpan: 1,
                rowSpan: 1,
              }))}

              renderItem={(item, index) => {
                // Ensure we have a valid item before rendering
                if (!item || !item.id) return null;

                // Find the matching element with proper type checking
                const updatedItem = elements.find(el => el.id === item.id);

                // If no matching item found, return null or a placeholder
                if (!updatedItem) return null;
                // console.log(updatedItem)
                // console.warn(elements)
                // Calculate rowSpan based on content


                // Create modified item with dynamic rowSpan

                return (

                  <BoardItem
                    key={item.id}

                    // Instead of style prop, use these AWS UI specific props
                    variant={expandedItems.has(item.id) ? "default" : "container"}

                    i18nStrings={{
                      dragHandleAriaLabel: "Drag handle",
                      dragHandleAriaDescription:
                        "Use Space or Enter to activate drag, arrow keys to move, Space or Enter to submit, or Escape to discard.",
                      resizeHandleAriaLabel: "Resize handle",
                      resizeHandleAriaDescription:
                        "Use Space or Enter to activate resize, arrow keys to move, Space or Enter to submit, or Escape to discard."
                    }}

                    header={
                      <Header
                        actions={
                          <Toggle
                            onChange={(detail) => { toggleElement(item.id) }}
                            checked={updatedItem.selected}
                          />
                        }
                      >
                        <Button
                          variant="link"
                          onClick={() => toggleExpansion(item.id)}
                        >
                          {item.label} {expandedItems.has(item.id) ? "▼" : "▶"}
                        </Button>
                      </Header>
                    }

                    settings={
                      <ButtonDropdown
                        items={[{ id: "remove", text: "Remove" }]}
                        ariaLabel="Board item settings"
                        variant="icon"
                        onItemClick={() => actions.removeItem()}
                      />
                    }

                  >
                    {expandedItems.has(item.id) && (
                      <Grid
                        gridDefinition={[
                          { colspan: { default: 12, xxs: 6 } },
                          { colspan: { default: 12, xxs: 6 } }
                        ]}
                      >
                        {updatedItem.inputs.map(input => (
                          <Box key={input.id}>
                            <Box >{input.label.split("_").join(" ")}</Box>
                            {input.label === "top_space" || input.label === "bottom_space" ? (
                              <Slider
                                onChange={({ detail }) => editValue(item.id, input.id, detail.value)}
                                value={input.value}
                                valueFormatter={value => value + ""}
                                max={50}
                                min={0}
                                step={10}
                                referenceValues={[0, 10, 20, 30, 40, 50]}
                              />) : input["rte"] === true ?
                              <>
                                <ReactQuill
                                  theme="snow"
                                  bg="white"
                                  modules={modules}
                                  formats={formats}
                                  value={input.value}
                                  onChange={(value, delta, source, editor) => {
                                    //const plainText = value.replace(/<[^>]*>/g, ""); // Remove HTML tags
                                    const charCount = editor.getLength();
                                    //if (charCount > input.maxLen) return;
                                    // if (charCount <= input.maxLen) {
                                    editValue(item.id, input.id, value);
                                    //setRteLen(charCount - 1);
                                    // }
                                  }}
                                />
                          
                              </>
                              : (<Input
                                onChange={({ detail }) => editValue(item.id, input.id, detail.value)}
                                value={input.value}
                                spellcheck
                              />)
                            }
                          </Box>)
                        )}
                      </Grid>
                    )}
                  </BoardItem>

                )
              }}
              i18nStrings={(() => {
                function createAnnouncement(
                  operationAnnouncement,
                  conflicts,
                  disturbed
                ) {
                  const conflictsAnnouncement =
                    conflicts.length > 0
                      ? `Conflicts with ${conflicts
                        .map(c => c.data.title)
                        .join(", ")}.`
                      : "";
                  const disturbedAnnouncement =
                    disturbed.length > 0
                      ? `Disturbed ${disturbed.length} items.`
                      : "";
                  return [
                    operationAnnouncement,
                    conflictsAnnouncement,
                    disturbedAnnouncement
                  ]
                    .filter(Boolean)
                    .join(" ");
                }
                return {
                  liveAnnouncementDndStarted: operationType =>
                    operationType === "resize"
                      ? "Resizing"
                      : "Dragging",
                  liveAnnouncementDndItemReordered: operation => {
                    const columns = `column ${operation.placement
                      .x + 1}`;
                    const rows = `row ${operation.placement.y +
                      1}`;
                    return createAnnouncement(
                      `Item moved to ${operation.direction === "horizontal"
                        ? columns
                        : rows
                      }.`,
                      operation.conflicts,
                      operation.disturbed
                    );
                  },
                  liveAnnouncementDndItemResized: operation => {
                    const columnsConstraint = operation.isMinimalColumnsReached
                      ? " (minimal)"
                      : "";
                    const rowsConstraint = operation.isMinimalRowsReached
                      ? " (minimal)"
                      : "";
                    const sizeAnnouncement =
                      operation.direction === "horizontal"
                        ? `columns ${operation.placement.width}${columnsConstraint}`
                        : `rows ${operation.placement.height}${rowsConstraint}`;
                    return createAnnouncement(
                      `Item resized to ${sizeAnnouncement}.`,
                      operation.conflicts,
                      operation.disturbed
                    );
                  },
                  liveAnnouncementDndItemInserted: operation => {
                    const columns = `column ${operation.placement
                      .x + 1}`;
                    const rows = `row ${operation.placement.y +
                      1}`;
                    return createAnnouncement(
                      `Item inserted to ${columns}, ${rows}.`,
                      operation.conflicts,
                      operation.disturbed
                    );
                  },
                  liveAnnouncementDndCommitted: operationType =>
                    `${operationType} committed`,
                  liveAnnouncementDndDiscarded: operationType =>
                    `${operationType} discarded`,
                  liveAnnouncementItemRemoved: op =>
                    createAnnouncement(
                      `Removed item ${op.item.data.title}.`,
                      [],
                      op.disturbed
                    ),
                  navigationAriaLabel: "Board navigation",
                  navigationAriaDescription:
                    "Click on non-empty item to move focus over",
                  navigationItemAriaLabel: item =>
                    item ? item.data.title : "Empty"
                };
              })()}
              onItemsChange={({ detail }) => { console.log(detail); setBoardItems(detail.items); handleReorder(detail.items); }}
            /> */}
            <DndContext collisionDetection={closestCorners} onDragMove={(error) => {
              // console.log("ondrag move");
              // setIsDragStarted(true)
            }} sensors={sensors} onDragEnd={handleDragEnd} >

              <SortableContext items={StoreModules} strategy={verticalListSortingStrategy}>

                <SpaceBetween size="l">
                  {StoreModules.map(module => (
                    <ButtonElement module={module} key={module.id} onDelete={handleModuleDelete} />
                  ))}
                </SpaceBetween>
              </SortableContext>

            </DndContext>

            {/* Module Selection */}
            <Select
              placeholder="Add Module"
              onChange={handleModuleAdd}
              options={optionalElements.map((element, index) => ({
                label: element.label,
                value: String(index),
                description: element.description
              }))}
              selectedOption={null}
              expandToViewport
            />
          </SpaceBetween>
        </Box>
      </SpaceBetween>
      <Modal
        onDismiss={() => setModalShow(false)}
        visible={ModalShow}
        footer={
          <Box float="right">
            <SpaceBetween direction="horizontal" size="xs">
              <Button variant="link" onClick={() => setModalShow(false)}>Cancel</Button>
              <Button variant="primary" onClick={() => {
                deleteModule(deleteModuleInfo.id)
                setModalShow(false)
              }}>Ok</Button>
            </SpaceBetween>
          </Box>
        }
        header={deleteModuleInfo.title + " Module"}
      >
        Are you sure you want to delete the {deleteModuleInfo.title} module ?
      </Modal>
    </Box>
  );
};

export default LeftPanel;
