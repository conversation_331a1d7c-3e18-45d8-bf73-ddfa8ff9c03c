@use '@amzn/awsui-design-tokens/polaris.scss' as awsui;

body {
width: 100% !important;
}

#harmony-nav {
  display: none !important;
}
.custom-home-text-inverted {
  color: awsui.$color-text-home-header-default;
}

.custom-home-text-secondary {
  color: awsui.$color-text-home-header-secondary;
}

@media (min-width: 992px) {
  .custom-home__sidebar {
    margin-top: -6rem;
  }
}

.custom-home-image__placeholder:before {
  content: 'X';
  display: block;
  background-color: #f2f3f3;
  color: #ffffff;
  text-align: center;
  font-size: 40rem;
  line-height: 40rem;
}

.custom-list-separator {
  list-style-type: none;
  margin: 0;
  padding: 0;

  li {
    border-top: 1px solid awsui.$color-border-divider-default;
    padding: 0.8rem 0;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    &:first-child {
      padding-top: 0;
      border-top: none;
    }

    &:last-child {
      padding-bottom: 0;
    }
  }
}

#main-content td table {
  // display: block ;
}
#email-preview.rtl  p,
#email-preview.rtl  a,
#email-preview.rtl  table{
  direction: rtl !important;
  text-align: right !important;
  margin-left: auto !important;    /* right align */
}

#email-preview.rtl #main-content table[align="left"] {
  float: right !important;
}

.awsui_header_9ckv7_1175u_36 {
  padding: 0 !important;
}

#email-preview p {
  margin: 0 !important;
}
#email-preview p a {
  color: rgb(0, 126, 185) !important;
}
.awsui_root_4yi2u_s8x8u_145:not(#\9) {
  // width: 50px !important;
}

.awsui_title-variant-h2_2qdw9_16hfn_307{
  display: flex !important;
  flex: 1 !important;
  width: 100% !important;
  justify-content: space-between
}

.delete-module-icon:hover {
  color: #ff9900 !important;
  cursor: pointer;
}

.awsui_padding-box_k5dlb_o7um0_178 {
  max-width: 1380px;
  margin: 0 auto;
}

.metadata-sender {
  
    display: flex;
    justify-content: space-between;
    width: 100%;
    
  
  
}

.awsui_header_14iqq_170i5_346 {
  cursor:grab !important;
}
#email-preview ol {
  margin : 0 !important;
}
#email-preview ol li[data-list="bullet"] {
  list-style-type: disc !important;
}

#right-panel .awsui_grid-column_14yj0_16am7_186 {
  align-self: center !important;
}
@media (prefers-color-scheme: dark) {
 
    // .darkMode{ background: #000000 !important;}
  
}

@font-face {
  font-family: 'AmazonEmber';
  src: url('../../assets/fonts/AmazonEmberDisplay_W_Bd.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'AmazonEmber-Bold';
  src: url('../../assets/fonts/AmazonEmberDisplay_W_Bd.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* If you have the regular weight version */
@font-face {
  font-family: 'AmazonEmber';
  src: url('../../assets/fonts/AmazonEmberDisplay_W_Rg.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}


#show-firstname {
  // border: 2px solid red;
  text-align: center;
}

.ql-size-small {
  font-size: 12px !important;
}

