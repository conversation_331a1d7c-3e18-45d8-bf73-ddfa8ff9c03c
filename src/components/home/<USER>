// RightPanel.tsx
import React, { useRef, useState, useEffect, useCallback } from "react";
import Box from '@amzn/awsui-components-react/polaris/box';
import Input from '@amzn/awsui-components-react/polaris/input';
import Header from '@amzn/awsui-components-react/polaris/header';
import Button from '@amzn/awsui-components-react/polaris/button';
import SpaceBetween from '@amzn/awsui-components-react/polaris/space-between';
import Modal from '@amzn/awsui-components-react/polaris/modal';
import Select from '@amzn/awsui-components-react/polaris/select';
import FormField from '@amzn/awsui-components-react/polaris/form-field';
import Alert from '@amzn/awsui-components-react/polaris/alert';
import ColumnLayout from "@amzn/awsui-components-react/polaris/column-layout";
import Toggle from "@amzn/awsui-components-react/polaris/toggle";
import Container from "@amzn/awsui-components-react/polaris/container";
import StatusIndicator from "@amzn/awsui-components-react/polaris/status-indicator";
import Spinner from '@amzn/awsui-components-react/polaris/spinner';
import { customAlphabet } from "nanoid";
import { useStore } from "../../data/store";
import parse from "html-react-parser";
import ModuleElement from "./ModuleElement";
import EmailShell from "./EmailShell";
import Flashbar from "@amzn/awsui-components-react/polaris/flashbar";
import ButtonDropdown from "@amzn/awsui-components-react/polaris/button-dropdown";
import Badge from "@amzn/awsui-components-react/polaris/badge";
import Grid from "@amzn/awsui-components-react/polaris/grid";
import Link from "@amzn/awsui-components-react/polaris/link";
import Popover from "@amzn/awsui-components-react/polaris/popover";
import KeyValuePairs from "@amzn/awsui-components-react/polaris/key-value-pairs";

// Import custom components and hooks
import LoadingOverlay from "../../components/common/LoadingOverlay";
import ErrorBoundary from "../../components/common/ErrorBoundary";
import { useEmail } from "../../hooks/useEmail";
import { useNotification } from "../../hooks/useNotification";
import { isValidFileName, isValidTrk, generateShareableUrl, formatCampaignId, prepareEmailData } from "../../utils/emailUtils";
import { EmailData, Template, RightPanelProps, Module } from "../../interfaces/email";
import { EMAIL_TYPES, LANGUAGE_OPTIONS, ERROR_MESSAGES } from "../../config/constants";
import { CampaignsApiEndpoint } from '../../config';

const RightPanel: React.FC<RightPanelProps> = () => {
  const user = window.harmony.api.getUser();
  if (user.login === "myself") {
    user.login = "fahdsk"
  }
  const alphanumericUniqueCode = customAlphabet('0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', 10);

  // Zustand store
  const { elements, optionalElements, metadata, reorderState, resetMetadata, editMetadata } = useStore();

  // Custom hooks
  const {
    isLoading,
    error,
    emailList,
    createEmail,
    getEmail,
    updateEmail,
    deleteEmail,
    listEmails
  } = useEmail();

  const {
    addNotification,
    getFlashbarItems
  } = useNotification();

  // State management with proper typing
  const [fileName, setFileName] = useState<string>("");
  const [currentTemplate, setCurrentTemplate] = useState<string>("Select Template");
  const [isExportModalVisible, setIsExportModalVisible] = useState<boolean>(false);
  const [isSaveModalVisible, setIsSaveModalVisible] = useState<boolean>(false);
  const [alertVisible, setAlertVisible] = useState<boolean>(false);
  const [RtlToggle, setRtlToggle] = useState<boolean>(false);
  const importBtn = useRef<HTMLInputElement>(null);
  const [emailType, setEmailType] = useState<{ label: string; value: string }>(EMAIL_TYPES.MARKETING);
  const [variant, setVariant] = useState<{ label: string; value: string }>({ label: "english", value: "english" });
  const [variantCustomInput, setVariantCustomInput] = useState<string>();
  const [emailData, setEmailData] = useState<{
    emailName: string;
    emailOwner: string;
  }>({ emailName: "", emailOwner: user.login });
  const [selectedCampaignId, setSelectedCampaignId] = useState<string | null>(null);
  const [flashbarItems, setFlashbarItems] = useState<Array<any>>([]);
  const [isCurrentUserOwner, setIsCurrentUserOwner] = useState<boolean>(true);
  const [emailInfoVisible, setEmailInfoVisible] = useState<boolean>(false);
  const [emailFullData, setEmailFullData] = useState<EmailData | null>(null);
  const [audienceSegmentValue, setAudienceSegmentValue] = useState<string>("");
  const [isHtmlExportModalVisible, setIsHtmlExportModalVisible] = useState<boolean>(false);

  // Memoized functions
  interface OptionType {
    label: string;
    value: string;
  }

  const handleEmailTypeChange = useCallback((value: OptionType) => {
    setEmailType(value);
  }, []);

  const handleVariantChange = useCallback((value: OptionType) => {
    setVariant(value);
    if (value.value !== "audience segment") {
      setAudienceSegmentValue("");
      editMetadata("variant", value.value);
    }
  }, [editMetadata]);

  const [selectedId, setSelectedId] = React.useState(
    "seg-1"
  );

  const trkRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  const isTrkValid = trkRegex.test(metadata.trk.value) || metadata.trk.value === ""

  useEffect(() => {
    console.warn("USER", user)
  }, [elements]);
  useEffect(() => {
    // This will run whenever metadata changes
    setEmailType({ label: metadata["OPER"] === true ? 'OPER' : 'Marketing', value: metadata["OPER"] === true ? 'oper' : 'marketing' });

    // Check if the variant is one of the standard languages
    const standardLanguages = ["english", "french", "german", "italian", "spanish", "portuguese", "turkish"];

    if (standardLanguages.includes(metadata?.variant)) {
      setVariant({ label: metadata.variant, value: metadata.variant });
      // Reset audience segment value when a standard language is selected
      setAudienceSegmentValue("");
    } else if (metadata?.variant) {
      // This is an audience segment or custom variant
      setVariant({ label: "audience segment", value: "audience segment" });
      // Set the audience segment value to the current variant
      setAudienceSegmentValue(metadata.variant);
    } else {
      // Default to English if no variant is specified
      setVariant({ label: "english", value: "english" });
      setAudienceSegmentValue("");
    }
  }, [metadata]);
  useEffect(() => {
    if (variant.label === "audience segment" && audienceSegmentValue) {
      editMetadata("variant", audienceSegmentValue);
    }
  }, [variant, audienceSegmentValue, editMetadata]);
  /**
   * Handles JSON file import
   * @param {React.ChangeEvent<HTMLInputElement>} e - File input change event
   */
  const importJson = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const file = e.target.files?.[0];

    if (!file || file.type !== "application/json") {
      setAlertVisible(true);
      return;
    }

    setFileName(file.name);

    const fileReader = new FileReader();
    fileReader.readAsText(file, "UTF-8");
    fileReader.onload = (e: ProgressEvent<FileReader>) => {
      if (e.target?.result) {
        const json: Template = JSON.parse(e.target.result as string);
        setCurrentTemplate(file.name);
        mapModulesToHTML(json);

        // Show notification
        addNotification("success", `File "${file.name}" imported successfully.`);
      }
    };
  };

  /**
   * Maps JSON modules to HTML elements
   * @param {Template} json - Template data
   */
  const mapModulesToHTML = (json: { metadata?: Metadata; modules?: Module[]; emailData?: { metadata: Metadata; modules: Module[] } }): void => {
    // Handle both direct Template objects and API response structure
    const metadata = json?.metadata || json?.emailData?.metadata;
    const modules = json?.modules || json?.emailData?.modules;
    console.log('Mapping modules:', modules);

    const moduleElements = modules.map((module: Module) => {
      let existingElement = elements.find(
        (element) => element.label === module.module_type
      );
      console.warn("existingElement", elements);

      if (!existingElement) {
        existingElement = optionalElements.find(
          (element) => element.label === module.module_type
        );
      }

      if (existingElement) {
        // Debug log for Title module
        if (module.module_type === "Title") {
          console.log("Title module inputs from API:", module);
          console.log("Title module existing element:", existingElement);
        }

        // Create a new element with the correct structure
        const newElement = {
          id: module?.id || alphanumericUniqueCode(),
          copy: module.copy,
          order: module.order,
          label: module.module_type,
          selected: true,
          // Create a new function that properly binds to the new element
          html: function () {
            // Call the original html function but bind it to this new element
            return existingElement.html.call(this);
          },
          // Start with a deep copy of the existing element's inputs
          inputs: JSON.parse(JSON.stringify(module.inputs))
        };


        return newElement;
      }
      return null;
    }).filter(Boolean);
    console.log("moduleElements", moduleElements);

    reorderState(moduleElements);
    resetMetadata(metadata);
  };

  const toggleClassById = (elementId: string, className: any, isChecked: boolean) => {
    const element = document.getElementById(elementId);
    if (element) {
      if (isChecked) {
        element.classList.add(className);
      } else {
        element.classList.remove(className);
      }
    }
  };
  /**
   * Handles JSON export
   */
  const exportJson = (): void => {
    interface ModuleTypes {
      [key: string]: number;
    }
    const TypesOfElement: ModuleTypes = {
      "Banner Header": 0,
      "Banner Hero": 0,
      "Banner Sub": 0,
      "Title": 0,
      "Paragraph": 0,
      "CTA": 0,
      "Sub Heading": 0,
      "Single Icon": 0,
      "2 Column Icons": 0,
      "Promo": 0,
      "Calendar": 0,
      "Featured Session Image": 0,
      "Featured Session Text": 0,
      "4 Column Icons": 0,
      "2 Column Images": 0,
      "Secondary CTA": 0,
    };



    // Update variant metadata with audience segment value if applicable
    const variantValue = variant.label === "audience segment" ? audienceSegmentValue : variant.value;
    editMetadata("variant", variantValue || "english");
    const getModuleCount = (element: Module): number => {
      TypesOfElement[element.label] = TypesOfElement[element.label] + 1;
      return TypesOfElement[element.label] - 1;
    };

    const modules: Module[] = elements
      .filter((element) => element.selected)
      .map((element, index) => ({
        id: alphanumericUniqueCode(),
        order: index,
        copy: getModuleCount(element),
        module_type: element.label,
        inputs: [...element.inputs],
      }));

    const json = {
      metadata,
      modules,
    };
    console.warn(json)
    // debugger
    const dataStr =
      "data:text/json;charset=utf-8," +
      encodeURIComponent(JSON.stringify(json, null, "\t"));
    const downloadJson = document.createElement("a");
    downloadJson.setAttribute("href", dataStr);
    downloadJson.setAttribute("download", `${fileName}.json`);
    document.body.appendChild(downloadJson);
    downloadJson.click();
    downloadJson.remove();
    setIsSaveModalVisible(false);
  };

  /**
   * Handles template selection
   */
  const selectTemplate = async (e: { detail: { selectedOption: { value: string; label: string } } }): Promise<void> => {
    const templateName = e.detail.selectedOption.value;
    try {
      const selectedTemplate = await import(`./templates/${templateName}.json`);
      console.log(selectedTemplate);
      setCurrentTemplate(e.detail.selectedOption.label);
      mapModulesToHTML(selectedTemplate.default);
    } catch (error) {
      console.error('Error loading template:', error);
      // Handle error appropriately
    }
  };

  /**
   * Handles email campaign selection
   */
  const selectEmailCampaign = async (e: { detail: { selectedOption: { value: string; label: string } } }): Promise<void> => {
    const campaignId = e.detail.selectedOption.value;
    setSelectedCampaignId(campaignId); // Store the selected campaign ID

    // Update the URL without reloading the page - using path parameter
    const baseUrl = `${window.location.origin}/maverick`;
    const newUrl = `${baseUrl}/${campaignId}`;
    window.history.pushState({}, '', newUrl);

    try {
      const response = await getEmail(campaignId);
      // Check for data property first, then fall back to body if needed
      const emailData = response;

      if (emailData) {
        console.log('Selected email campaign:', emailData);

        // Store the full email data for the info popover
        setEmailFullData(emailData);

        setCurrentTemplate(e.detail.selectedOption.label);

        // Check if current user is the owner
        const isOwner = emailData.emailOwner === user.login;
        setIsCurrentUserOwner(isOwner);

        // Set the email data from the response
        if (emailData.emailName && emailData.emailOwner) {
          setEmailData({
            emailName: emailData.emailName,
            emailOwner: emailData.emailOwner
          });
        }

        // Map the email data to the UI - following importJson pattern
        const templateData = emailData.emailData;
        console.log('templateData:', templateData);
        mapModulesToHTML(templateData);

        // Show notification
        addNotification("success", `Email campaign "${e.detail.selectedOption.label}" loaded successfully.`);
      }
    } catch (error) {
      console.error('Error loading email campaign:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      addNotification("error", `Failed to load email campaign: ${errorMessage}`);
    }
  };

  const saveEmail = async (): Promise<void> => {
    try {
      // Generate a random 8-character string using nanoid
      const randomString = alphanumericUniqueCode();

      // Create campaignId with the desired format
      const campaignId = formatCampaignId(
        emailData.emailOwner,
        emailData.emailName,
        randomString
      );

      // Prepare email data payload
      const payload = prepareEmailData(campaignId, emailData, metadata, elements);

      const response: CloneResponse = await createEmail(payload);
      // Check for data property first, then fall back to body if needed
      const responseData = response.data || response;

      if (response && responseData) {
        // Get the campaignId from the API response
        const campaignId = responseData.campaignId;

        // Set the selectedCampaignId to update the button text
        setSelectedCampaignId(campaignId);

        // Update the URL without reloading the page - using path parameter
        const baseUrl = `${window.location.origin}/maverick`;
        const newUrl = `${baseUrl}/${campaignId}`;
        window.history.pushState({}, '', newUrl);

        // Generate shareable URL
        const shareableUrl = generateShareableUrl(campaignId);

        // Copy to clipboard
        navigator.clipboard.writeText(shareableUrl)
          .then(() => {
            addNotification("info", "Shareable URL copied to clipboard!");
          })
          .catch(err => {
            console.error('Failed to copy URL: ', err);
          });

        // Refresh email list
        await listEmails(user.login);
        setIsSaveModalVisible(false);

        // Show notification
        addNotification("success", `Email "${emailData.emailName}" saved successfully.`);
      }
    } catch (error) {
      console.error('Error saving email:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      addNotification("error", `Failed to save email: ${errorMessage}`);
    }
  };

  const handleSaveOrUpdate = async (): Promise<void> => {
    if (selectedCampaignId) {
      if (isCurrentUserOwner) {
        // Update existing campaign
        try {
          const updateData = prepareEmailData(selectedCampaignId, emailData, metadata, elements);

          const response = await updateEmail(selectedCampaignId, updateData);
          // Check for data property first, then fall back to body if needed
          const responseData = response.data || response;

          if (response && responseData) {
            // Refresh email list
            await listEmails(user.login);
            setIsSaveModalVisible(false);
            // Show notification
            addNotification("success", `Email "${emailData.emailName}" updated successfully.`);
          }
        } catch (error) {
          console.error('Error updating email:', error);
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          addNotification("error", `Failed to update email: ${errorMessage}`);
        }
      } else {
        // Clone the email for the current user
        await cloneEmail();
      }
    } else {
      // Save new campaign
      await saveEmail();
    }
  };

  // Add a function to clone an email
  const cloneEmail = async (): Promise<void> => {
    interface CloneResponse {
      data?: {
        campaignId: string;
      };
      error?: {
        message: string;
      };
      campaignId?: string;
    }
    try {
      // Generate a random 8-character string using nanoid
      const randomString = alphanumericUniqueCode();

      // Create a clone name
      const cloneName = `${emailData.emailName}-cloned`;

      // Create campaignId with the desired format
      const campaignId = formatCampaignId(
        user.login,
        cloneName,
        randomString
      );

      // Prepare email data payload for cloning
      const payload = prepareEmailData(campaignId,
        { emailName: cloneName, emailOwner: user.login },
        metadata,
        elements
      );

      console.warn("payload: ", payload);

      const response = await createEmail(payload);
      // Check for data property first, then fall back to body if needed
      const responseData = response.data || response;

      // Check if response exists and has valid data
      if (response && responseData) {
        // Get the campaignId from the API response
        const campaignId = responseData.campaignId;

        // Set the selectedCampaignId to update the button text
        setSelectedCampaignId(campaignId);

        // Update the URL without reloading the page - using path parameter
        const baseUrl = `${window.location.origin}/maverick`;
        const newUrl = `${baseUrl}/${campaignId}`;
        window.history.pushState({}, '', newUrl);

        // Update email data with new owner
        setEmailData({
          emailName: `${emailData.emailName}-cloned`,
          emailOwner: user.login
        });

        // Set current user as owner
        setIsCurrentUserOwner(true);

        // Refresh email list
        await listEmails(user.login);
        setIsSaveModalVisible(false);

        // Show notification
        addNotification("success", `Email "${emailData.emailName}" cloned successfully.`);
      } else {
        // Handle case where response exists but has an error status
        const errorMessage = response && response.error ?
          response.error.message :
          'Failed to clone email: Server returned an invalid response';

        console.error('Error response:', response);
        addNotification("error", errorMessage);
      }
    } catch (error) {
      console.error('Error cloning email:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      addNotification("error", `Failed to clone email: ${errorMessage}`);
    }
  };

  useEffect(() => {
    console.log('Attempting to fetch emails for user:', user.login);
    listEmails(user.login).catch(error => {
      console.error('Error fetching emails:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      addNotification("error", `Failed to fetch emails: ${errorMessage}`);
    });
  }, [listEmails, user.login, addNotification]); // Removed emailList.length dependency

  // Add this useEffect to load email from URL path when component mounts
  useEffect(() => {
    async function loadEmailFromUrl(): Promise<void> {
      // Get emailId from URL path (last segment)
      const pathSegments = window.location.pathname.split('/').filter(segment => segment);
      const emailId = pathSegments[pathSegments.length - 1];


      // Check if emailId looks like a campaign ID (contains underscores and is not 'maverick')
      if (emailId && emailId !== 'maverick' && emailId.includes('_')) {
        try {
          const response = await getEmail(emailId);

          console.log('****Email loaded from URL:', response);
          if (response) {


            // Set the selected campaign ID
            setSelectedCampaignId(emailId);

            // Check if current user is the owner
            const isOwner = response.emailOwner === user.login;
            setIsCurrentUserOwner(isOwner);

            // Set email data
            if (response.emailName && response.emailOwner) {
              setEmailData({
                emailName: response.emailName,
                emailOwner: response.emailOwner
              });
            }
            // Store the full email data for the info popover
            setEmailFullData(response);
            // Set current template name
            setCurrentTemplate(response.emailName || 'Loaded Template');

            // Map the email data to the UI
            const templateData = response.emailData;
            console.log("templateData: ", templateData);

            mapModulesToHTML(templateData);

            // Show notification
            addNotification("success", `Email campaign "${response.emailName}" loaded successfully.`);
          }
        } catch (error) {
          console.error('Error loading email from URL:', error);
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          addNotification("error", `Failed to load email: ${errorMessage}`);
        }
      }
    }

    // Load email from URL when component mounts
    loadEmailFromUrl();

    // Also fetch the email list when component mounts
    async function fetchEmails() {
      try {
        const response = await listEmails(user.login);
        console.warn('initial load', response);

        if (response && response.error) {
          console.error('Error fetching emails:', response.error);
          addNotification("error", `Failed to fetch emails: ${response.error.message}`);
        } else {
          console.log('Email list loaded successfully', response);
        }
      } catch (error) {
        console.error('Error fetching emails:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        addNotification("error", `Failed to fetch emails: ${errorMessage}`);
      }
    }

    fetchEmails();
  }, [listEmails, user.login, addNotification, getEmail]); // Removed emailList.length dependency

  // Add a resetEditor function to handle resetting the state
  const resetEditor = () => {
    // Reset URL to base path without campaign ID
    const baseUrl = `${window.location.origin}/maverick`;
    window.history.pushState({}, '', baseUrl);

    // Reset state variables
    setSelectedCampaignId(null);
    setCurrentTemplate("Select Template");
    setFileName("");
    setEmailData({
      emailName: "",
      emailOwner: user.login
    });
    setIsCurrentUserOwner(true);
    // Reset variant and audience segment
    setVariant({ label: "english", value: "english" });
    setAudienceSegmentValue("");
    editMetadata("variant", "english");

    // Reset to default elements using the store's resetToDefault function
    const resetToDefault = useStore.getState().resetToDefault;
    resetToDefault();

    // Show notification
    addNotification("info", "Email editor has been reset to default state");
  };

  // Add this function to export the email as HTML
  const exportHtml = (): void => {
    try {
      // Get the email preview container
      const emailPreview = document.getElementById('email-preview');

      if (!emailPreview) {
        addNotification("error", "Could not find email preview element");
        return;
      }

      // Clone the element to avoid modifying the original
      const emailClone = emailPreview.cloneNode(true) as HTMLElement;

      // Get the HTML content
      let htmlContent = emailClone.outerHTML;

      // Add proper DOCTYPE and meta tags if needed
      const doctype = '<!DOCTYPE html>';

      // Create the full HTML document
      const fullHtml = `${doctype}
${htmlContent}`;

      // Create a blob with the HTML content
      const blob = new Blob([fullHtml], { type: 'text/html' });

      // Create a URL for the blob
      const url = URL.createObjectURL(blob);

      // Create a download link
      const downloadLink = document.createElement('a');
      downloadLink.href = url;


      // Use emailName from emailData if available, otherwise use fileName or default
      const exportFileName = emailData.emailName.trim() || fileName.trim() || 'email-preview';

      downloadLink.download = `${exportFileName}.html`;

      // Append the link to the body
      document.body.appendChild(downloadLink);

      // Click the link to trigger the download
      downloadLink.click();

      // Clean up
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(url);

      addNotification("success", "HTML exported successfully");
    } catch (error) {
      console.error('Error exporting HTML:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      addNotification("error", `Failed to export HTML: ${errorMessage}`);
    }
  };

  // Add this function to format the email data for display
  interface EmailInfoItem {
    label: string;
    value: string;
  }

  const formatEmailInfoData = (emailData: APIResponse | null): EmailInfoItem[] => {
    // Check for data property first, then fall back to body if needed
    if (!emailData) return [];

    const emailVariant = emailData.emailData?.metadata?.variant || "-";

    // Format the variant display value
    let displayVariant = emailVariant;
    if (variant.label === "audience segment" && variant.value) {
      displayVariant = variant.value;
    }

    const items = [
      { label: "Campaign ID", value: emailData.campaignId || "-" },
      { label: "Email Owner", value: emailData.emailOwner || "-" },
      { label: "Email Name", value: emailData.emailName || "-" },
      { label: "Email Type", value: emailData.emailData?.metadata?.OPER ? "OPER" : "Marketing" },
      { label: "Created At", value: emailData.createdAt ? new Date(emailData.createdAt).toLocaleString() : "-" },
      { label: "Updated At", value: emailData.updatedAt ? new Date(emailData.updatedAt).toLocaleString() : "-" },
      { label: "Variant", value: displayVariant },
      { label: "Modules Count", value: emailData.emailData?.modules?.length.toString() || "0" }
    ];

    return items;
  };

  // Add a debugging useEffect
  useEffect(() => {
    console.log("Variant state:", variant);
    console.log("Audience segment value:", audienceSegmentValue);
  }, [variant, audienceSegmentValue]);

  return (
    <Box margin={{ left: "xl" }} id="right-panel">
      {isLoading && <LoadingOverlay />}
      {flashbarItems.length > 0 && <Flashbar items={flashbarItems} />}
      <SpaceBetween size="l">
        <Header variant="h2">
          Email Preview &nbsp;
          <Popover
            dismissAriaLabel="Close"
            position="bottom"
            size="large"
            header="Email Information"
            content={
              <KeyValuePairs
                columns={2}
                items={formatEmailInfoData(emailFullData)}
              />
            }
            triggerType="custom"
          >
            <Link
              variant="info"
              onFollow={(e) => {
                e.preventDefault();
                setEmailInfoVisible(!emailInfoVisible);
              }}
            >
              Email Info
            </Link>
          </Popover>
        </Header>

        <Grid
          gridDefinition={[{ colspan: 6 }, { colspan: 2.5 }, { colspan: 2.5 }, { colspan: 0.5 }]}>
          <Select
            selectedOption={
              selectedCampaignId
                ? { label: currentTemplate, value: selectedCampaignId }
                : null
            }
            onChange={({ detail }: { detail: { selectedOption: OptionType } }) => {
              const option = detail.selectedOption;
              // Check if this is a template or a campaign
              if (option?.value?.startsWith('email_template_')) {
                selectTemplate({ detail: { selectedOption: option } });
              } else if (option?.value !== 'no-campaigns') {
                selectEmailCampaign({ detail: { selectedOption: option } });
              }
            }}
            placeholder={`Select Campaign or Template (${emailList.length} campaigns)`}
            filteringType="auto"
            virtualScroll

            options={[
              {
                label: "Your Campaigns",
                options: emailList.length > 0 ? emailList : [
                  { label: "No campaigns found", value: "no-campaigns", disabled: true }
                ]
              },
              {
                label: "Best Performing Templates",
                disabled: true,
                options: [1, 2, 3, 4, 5].map((num) => ({
                  label: `Template ${num}`,
                  value: `email_template_${num}`,
                }))
              }
            ]}
          />

          <SpaceBetween direction="horizontal" size="xs">
            {/* Email Type Badge */}
            <Badge color={emailType.value === "oper" ? "red" : "blue"}>
              {emailType.label}
            </Badge>

            {/* Variant Badge - Show actual value for audience segment */}
            <Badge color="green">
              {variant.label === "audience segment" ? audienceSegmentValue || "audience segment" : variant.label}
            </Badge>
          </SpaceBetween>
          <Button
            variant="primary"
            onClick={() => setIsSaveModalVisible(true)}
          >
            {selectedCampaignId
              ? (isCurrentUserOwner ? 'Update Email' : 'Clone Email')
              : 'Save Email'}

            <FormField>
              <input
                type="file"
                ref={importBtn}
                onChange={importJson}
                accept="application/json"
                style={{ display: 'none' }}
              />
            </FormField>
          </Button>


          {/* <Box float="right">
            <Toggle
              onChange={({ detail }) => {
                setRtlToggle(detail.checked)
                toggleClassById("email-preview", "rtl", detail.checked)
                editMetadata("RTL", detail.checked)
              }}
              checked={RtlToggle}
            >
              RTL
            </Toggle>
          </Box> */}




          <ButtonDropdown
            items={[
              {
                id: "import",
                text: "Import JSON"
              },
              {
                id: "export",
                text: "Export JSON"
              },
              {
                id: "exportHtml",
                text: "Export HTML"
              },
              {
                id: "reset",
                text: "Reset Editor"
              },
              selectedCampaignId && isCurrentUserOwner ? {
                id: "delete",
                text: "Delete Email"
              } : {
                id: "",
                text: ""
              }
            ]}
            onItemClick={({ detail }) => {
              if (detail.id === "import") {
                importBtn.current?.click();
              } else if (detail.id === "export") {
                setIsExportModalVisible(true);
              } else if (detail.id === "exportHtml") {
                if (!fileName.trim() && !emailData.emailName.trim()) {
                  // If no filename is set, open a modal to get it
                  setIsHtmlExportModalVisible(true);
                } else {
                  exportHtml();
                }
              } else if (detail.id === "reset") {
                resetEditor();
              }
            }}
            ariaLabel="File operations"
            variant="icon"
          />
        </Grid>

        {/* Email Content */}
        <div style={{ margin: '0 auto', width: '100%', maxWidth: "680px" }}>
          <Container disableContentPaddings
            disableHeaderPaddings
            fitHeight>

            <EmailShell emailType={emailType}>
              <div id="main-content">
                {elements
                  .filter((element) => element.selected)
                  .map((element) => (
                    <ModuleElement
                      key={element.id}
                      html={parse(typeof element.html === 'function' ? element.html() : '')}
                      inputs={element.inputs}
                      id={element.id}
                    />
                  ))}
              </div>

            </EmailShell>
          </Container>
        </div>

        {/* Export Modal */}
        <Modal
          visible={isExportModalVisible}
          onDismiss={() => setIsExportModalVisible(false)}
          header="Export your Maverick (JSON) File"
          footer={
            <SpaceBetween direction="horizontal" size="xs">
              <Button
                variant="link"
                onClick={() => setIsExportModalVisible(false)}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={exportJson}
                disabled={!fileName.trim() || !isTrkValid}
                disabledReason={!fileName.trim() ? "File name is required, please provide a filename to export" : undefined}

              >
                Export
              </Button>
            </SpaceBetween>
          }
        >
          <SpaceBetween size="xl">
            <ColumnLayout columns={2} >
              <FormField label="Variant" stretch>
                <Select
                  expandToViewport
                  selectedOption={variant}
                  onChange={({ detail }) => {
                    const selectedOption = detail.selectedOption;
                    if (selectedOption && selectedOption.value && selectedOption.label) {
                      setVariant({ label: selectedOption.label, value: selectedOption.value });

                      if (selectedOption.label === "audience segment") {
                        // Keep the existing audience segment value if switching back to it
                        if (audienceSegmentValue) {
                          editMetadata("variant", audienceSegmentValue);
                        }
                      } else {
                        // For standard languages, update the metadata and clear audience segment
                        editMetadata("variant", selectedOption.value);
                        setAudienceSegmentValue("");
                      }
                    }
                  }}
                  options={[
                    { label: "english", value: "english" },
                    { label: "french", value: "french" },
                    { label: "spanish", value: "spanish" },
                    { label: "german", value: "german" },
                    { label: "italian", value: "italian" },
                    { label: "portuguese", value: "portuguese" },
                    { label: "turkish", value: "turkish" },
                    { label: "audience segment", value: "audience segment" }
                  ]}
                />

                {/* Separate conditional rendering for the audience segment input */}
                {variant.label === "audience segment" && (
                  <div style={{ marginTop: '8px' }}>
                    <FormField label="Enter audience segment">
                      <Input
                        value={audienceSegmentValue}
                        onChange={({ detail }) => {
                          console.log("Input changed:", detail.value);
                          setAudienceSegmentValue(detail.value);
                          editMetadata("variant", detail.value);
                        }}
                        placeholder="eg. English - TDM"
                      />
                    </FormField>
                  </div>
                )}
              </FormField>
              <FormField label="Email Type" stretch>
                <Select
                  expandToViewport
                  selectedOption={emailType}
                  onChange={({ detail }) => {
                    const selectedOption = detail.selectedOption;
                    if (selectedOption && selectedOption.value && selectedOption.label) {
                      setEmailType({ label: selectedOption.label, value: selectedOption.value });
                      if (selectedOption.value === "oper") {
                        editMetadata("OPER", true);
                      } else {
                        editMetadata("OPER", false);
                      }
                      console.log(selectedOption);
                    }
                  }}
                  options={[
                    { label: "Marketing", value: "marketing" },
                    { label: "OPER", value: "oper" },
                  ]}
                  placeholder="Email Type"
                />
              </FormField>
            </ColumnLayout>

            <FormField label="File name" description="Use descriptive names that include the purpose, language variant, and date (e.g., 'welcome-email-fr-20240115'). Avoid generic names like 'template1' or 'export'. Use hyphens to separate words."
              errorText={fileName.trim().length > 0 ? undefined : "File name is required"}>
              <Input
                value={fileName.split(".")[0]}
                onChange={({ detail }) => setFileName(detail.value)}
                placeholder="Enter your file name here"
              />
            </FormField>
            {!isTrkValid && (<StatusIndicator type="error">Please enter a valid TRK code to export this file</StatusIndicator>)}
          </SpaceBetween>
        </Modal>

        {/* Save Email Modal */}
        <Modal
          visible={isSaveModalVisible}
          onDismiss={() => setIsSaveModalVisible(false)}
          header={
            selectedCampaignId
              ? (isCurrentUserOwner ? "Update Email Campaign" : "Clone Email Campaign")
              : "Save Email Campaign"
          }
          footer={
            <SpaceBetween direction="horizontal" size="xs">
              <Button
                variant="link"
                onClick={() => setIsSaveModalVisible(false)}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleSaveOrUpdate}
                disabled={!emailData.emailName.trim() || !emailData.emailOwner.trim()}
              >
                {selectedCampaignId
                  ? (isCurrentUserOwner ? 'Update' : 'Clone')
                  : 'Save'}
              </Button>
            </SpaceBetween>
          }
        >
          <SpaceBetween size="l">
            {/* Add Variant and Email Type fields */}
            <ColumnLayout columns={2}>
              <FormField label="Variant" stretch>
                <Select
                  expandToViewport
                  selectedOption={variant}
                  onChange={({ detail }) => {
                    const selectedOption = detail.selectedOption;
                    if (selectedOption && selectedOption.value && selectedOption.label) {
                      setVariant({ label: selectedOption.label, value: selectedOption.value });

                      if (selectedOption.label === "audience segment") {
                        // Don't update metadata yet for audience segment
                        console.log("Audience segment selected");
                      } else {
                        // For standard languages, update the metadata directly
                        editMetadata("variant", selectedOption.value);
                      }
                    }
                  }}
                  options={[
                    { label: "english", value: "english" },
                    { label: "french", value: "french" },
                    { label: "spanish", value: "spanish" },
                    { label: "german", value: "german" },
                    { label: "italian", value: "italian" },
                    { label: "portuguese", value: "portuguese" },
                    { label: "turkish", value: "turkish" },
                    { label: "audience segment", value: "audience segment" }
                  ]}
                />

                {/* Separate conditional rendering for the audience segment input */}
                {variant.label === "audience segment" && (
                  <div style={{ marginTop: '8px' }}>
                    <FormField label="Enter audience segment">
                      <Input
                        value={audienceSegmentValue}
                        onChange={({ detail }) => {
                          console.log("Input changed:", detail.value);
                          setAudienceSegmentValue(detail.value);
                          editMetadata("variant", detail.value);
                        }}
                        placeholder="eg. English - TDM"
                      />
                    </FormField>
                  </div>
                )}
              </FormField>
              <FormField label="Email Type" stretch>
                <Select
                  expandToViewport
                  selectedOption={emailType}
                  onChange={({ detail }) => {
                    const selectedOption = detail.selectedOption;
                    if (selectedOption && selectedOption.value && selectedOption.label) {
                      setEmailType({ label: selectedOption.label, value: selectedOption.value });
                      if (selectedOption.value === "oper") {
                        editMetadata("OPER", true);
                      } else {
                        editMetadata("OPER", false);
                      }
                    }
                  }}
                  options={[
                    { label: "Marketing", value: "marketing" },
                    { label: "OPER", value: "oper" },
                  ]}
                  placeholder="Email Type"
                />
              </FormField>
            </ColumnLayout>
            {/* Email Name and Owner fields */}
            <FormField
              label="Email Name"
              description="Enter a descriptive name for this email campaign"
              errorText={emailData.emailName.trim().length > 0 ? undefined : "Email name is required"}
            >
              <Input
                value={emailData.emailName}
                onChange={({ detail }) => setEmailData(prev => ({ ...prev, emailName: detail.value }))}
                placeholder="e.g., Welcome Email - English"
              />
            </FormField>

            <FormField
              label="Email Owner"
              description={selectedCampaignId && !isCurrentUserOwner ? "You will become the owner of this cloned email" : ""}
              errorText={emailData.emailOwner.trim().length > 0 ? undefined : "Email owner is required"}
            >
              <Input
                value={selectedCampaignId && !isCurrentUserOwner ? user.login : emailData.emailOwner}
                readOnly={true}
                placeholder="e.g., Marketing Team"
              />
            </FormField>


          </SpaceBetween>
        </Modal>
        {/* HTML Export Modal */}
        <Modal
          visible={isHtmlExportModalVisible}
          onDismiss={() => setIsHtmlExportModalVisible(false)}
          header="Export Email as HTML"
          footer={
            <SpaceBetween direction="horizontal" size="xs">
              <Button
                variant="link"
                onClick={() => setIsHtmlExportModalVisible(false)}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={() => {
                  if (fileName.trim()) {
                    setIsHtmlExportModalVisible(false);
                    exportHtml();
                  }
                }}
                disabled={!fileName.trim() && !emailData.emailName.trim()}
              >
                Export HTML
              </Button>
            </SpaceBetween>
          }
        >
          <FormField
            label="Email name"

            errorText={fileName.trim().length > 0 ? undefined : "File name is required"}
          >
            <Input
              value={fileName || emailData.emailName}
              onChange={({ detail }) => setFileName(detail.value)}
              placeholder="Enter your Email HTML file name here"
            />
          </FormField>
        </Modal>

        {/* Error Alert */}
        <Alert
          visible={alertVisible}
          type="error"
          dismissible
          onDismiss={() => setAlertVisible(false)}
          header="Invalid File Format"
        >
          Please import JSON file to proceed
        </Alert>
      </SpaceBetween>
    </Box >
  );
};

export default RightPanel;
