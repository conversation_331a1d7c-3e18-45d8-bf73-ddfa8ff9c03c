// Popup.tsx
import React from "react";
import Modal from "@amzn/awsui-components-react/polaris/modal";
import Button from "@amzn/awsui-components-react/polaris/button";
import Box from "@amzn/awsui-components-react/polaris/box";
import SpaceBetween from "@amzn/awsui-components-react/polaris/space-between";

const Popup: React.FC = () => {
  const [visible, setVisible] = React.useState(false);

  return (
    <>
      <Button onClick={() => setVisible(true)}>Open Modal</Button>

      <Modal
        visible={visible}
        onDismiss={() => setVisible(false)}
        header="Modal Title"
        footer={
          <SpaceBetween direction="horizontal" size="xs">
            <Button onClick={() => setVisible(false)}>
              Close
            </Button>
            <Button variant="primary">
              Secondary Action
            </Button>
          </SpaceBetween>
        }
      >
        <Box>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
          Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        </Box>
      </Modal>
    </>
  );
};

export default Popup;
