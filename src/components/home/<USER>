
import TopNavigation from '@amzn/awsui-components-react/polaris/top-navigation';

import HomepageContent from './HomepageContent';
// import { ServiceNavigation } from '../navigation';
import './styles.scss';

export default function ServiceHomepage({ isLoading, isCurrentUserOwner }) {
  const user = window.harmony.api.getUser();

  // If loading, show a loading indicator
  if (isLoading) {
    return (
      <>
        <TopNavigation identity={{
          href: "#",
          title: "🚀 Maverick",
          logo: { src: "" }
        }}
        // Rest of TopNavigation props
        />
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: 'calc(100vh - 60px)',
          fontSize: '18px'
        }}>
          Loading email...
        </div>
      </>
    );
  }

  return (
    <>
      <TopNavigation identity={{
        href: "#",
        title: "🚀 Maverick",
        logo: { src: "" }
      }}
        utilities={[
          {
            type: "button",
            text: `Welcome ${user.firstName} 👋`,
            href: `https://phonetool.amazon.com/users/${user.login}`,
            external: true,
            target: "_blank",
            rel: "noopener noreferrer"
          },
          {
            type: "button",
            text: "Wiki Page",
            href: "https://w.amazon.com/bin/view/AWS/EMEA/Marketing/EMEADigital/Automation/Guides/Workfront/Maverick#History",
            external: true,
            externalIconAriaLabel: " (opens in a new tab)",
            target: "_blank",
            rel: "noopener noreferrer"
          },
          {
            type: "button",
            text: "How To Use ?",
            href: "https://broadcast.amazon.com/playlists/44155",
            external: true,
            externalIconAriaLabel: " (opens in a new tab)",
            target: "_blank",
            rel: "noopener noreferrer"
          },
          {
            type: "button",
            text: "Please Leave Feedback Here!",
            href: "https://survey.fieldsense.whs.amazon.dev/survey/9d757be2-1b19-42ed-8d8b-4b45043544ef",
            external: true,
            externalIconAriaLabel: " (opens in a new tab)",
            target: "_blank",
            rel: "noopener noreferrer"
          }


        ]}

      />
      {/* <AppLayout
        // navigation={<ServiceNavigation />}
        content={<HomepageContent />}
        contentType="default"
        disableContentPaddings={true}
        navigationOpen={navigationOpen}
        // onNavigationChange={event => setNavigationOpen(event.detail.open)}
        toolsHide={true}
      /> */}
      <HomepageContent isCurrentUserOwner={isCurrentUserOwner} />
    </>
  );
}
