
// @ts-nocheck
// This file contains HTML email template code which uses kebab-case CSS properties
// that are not compatible with React's CSSProperties type.
// We're using @ts-nocheck to bypass type checking for this file.

interface EmailElement {
  id: string;
  html: () => string;  // change this to be a function
  inputs: any[];
  label: string;
  selected: boolean;
}

interface EmailMetadata {
  subject: {
    label: string;
    value: string;
    maxLen: number;
  };
  preheader: {
    label: string;
    value: string;
    maxLen: number;
  };
  trk: {
    label: string;
    value: string;
    maxLen: number;
  };
  sender: string;
}

// EmailShell.tsx
import React from "react";
import Box from '@amzn/awsui-components-react/polaris/box';
import Container from '@amzn/awsui-components-react/polaris/container';
import SpaceBetween from '@amzn/awsui-components-react/polaris/space-between';
import { useStore } from "../../data/store";
import parse from "html-react-parser";
import ModuleElement from "./ModuleElement";
//   import { EmailElement, EmailMetadata } from "./types/email";

interface EmailShellProps {
  children?: React.ReactNode;
  emailType: {
    label: string;
    value: string;
  };
}

const EmailShell: React.FC<EmailShellProps> = (props) => {
  const { children, emailType } = props

  console.log(children, emailType)

  return (

    <html id="email-preview" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
      xmlns:o="urn:schemas-microsoft-com:office:office">

      <head>
        <title>Maverick Email Preview</title>
        {/* Always use in every email*/}
        <meta httpEquiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta content="width=device-width, minimal-ui, initial-scale=1.0, maximum-scale=1.0, user-scalable=0;" name="viewport" />
        <meta content="telephone=no" name="format-detection" />
        {/* For Dark Mode Code ---*/}
        <meta name="color-scheme" content="light dark" />
        <meta name="supported-color-schemes" content="light dark only" />
        {/* ****** THE STYLES BELOW ARE MODIFIED (ex: Darkmode logic), ORIGINAL STYLE CODE IS IN aura-container-wrap.html */}
        <style type="text/css" dangerouslySetInnerHTML={{ __html: "\n        @font-face{ font-family: 'AmazonEmber-Light'; src: url('https://pages.awscloud.com/rs/112-TZM-766/images/AmazonEmberDisplay_W_Lt.woff2') format('woff2'), url('https://pages.awscloud.com/rs/112-TZM-766/images/AmazonEmberDisplay_W_Lt.woff') format('woff'), url('https://pages.awscloud.com/rs/112-TZM-766/images/AmazonEmberDisplay_W_Lt.eot') format('eot'); font-weight: normal; font-style: normal;} @font-face{ font-family: 'AmazonEmber'; src: url('https://pages.awscloud.com/rs/112-TZM-766/images/AmazonEmberDisplay_W_Rg.woff2') format('woff2'), url('https://pages.awscloud.com/rs/112-TZM-766/images/AmazonEmberDisplay_W_Rg.woff') format('woff'), url('https://pages.awscloud.com/rs/112-TZM-766/images/AmazonEmberDisplay_W_Rg.eot') format('eot'), url('https://pages.awscloud.com/rs/112-TZM-766/images/AmazonEmberDisplay_W_Rg.ttf') format('truetype'); font-weight: normal; font-style: normal;} @font-face{ font-family: 'AmazonEmber-Bold'; src: url('https://pages.awscloud.com/rs/112-TZM-766/images/AmazonEmberDisplay_W_Bd.woff2') format('woff2'), url('https://pages.awscloud.com/rs/112-TZM-766/images/AmazonEmberDisplay_W_Bd.woff') format('woff'), url('https://pages.awscloud.com/rs/112-TZM-766/images/AmazonEmberDisplay_W_Bd.eot') format('eot'), url('https://pages.awscloud.com/rs/112-TZM-766/images/AmazonEmberDisplay_W_Bd.ttf') format('truetype'); font-weight: bold; font-style: normal;} @font-face{ font-family: 'AmazonEmber-Heavy'; src: url('https://pages.awscloud.com/rs/112-TZM-766/images/AmazonEmberDisplay_W_He.woff2') format('woff2'), url('https://pages.awscloud.com/rs/112-TZM-766/images/AmazonEmberDisplay_W_He.woff') format('woff'), url('https://pages.awscloud.com/rs/112-TZM-766/images/AmazonEmberDisplay_W_He.eot') format('eot'); font-weight: normal; font-style: normal;}\n\n        :root{ color-scheme: light dark; supported-color-schemes: light dark;} .font-family-decor{ font-family: 'AmazonEmber';} strong{ font-weight: normal !important; font-family: AmazonEmber-Heavy, Helvetica, arial, sans-serif !important;} b{ font-weight: normal !important; font-family: AmazonEmber-Heavy, Helvetica, arial, sans-serif !important;}\n\n        " }} /><style type="text/css" dangerouslySetInnerHTML={{ __html: "@media (prefers-color-scheme: dark){ [data-ogsc] .btn-darkMode{ color: #fefefe !important; background-color: #161D26 !important; } [data-ogsc] .btn-darkMode a{ color: #161D26 !important;} [data-ogsc] .btn-darkMode2{ border-color: #0972D3 !important; color: #0972D3 !important;} [data-ogsc] .btn-darkMode2 a{ color: #161D26 !important;} [data-ogsc] .white{ color: #ffffff !important;} [data-ogsc][data-ogsc] .dark{ color: #232F3E !important;} [data-ogsc] .dark2{ color: #888888 !important;} [data-ogsc] .lightMode{ background: #fefefe !important;} [data-ogsc] .lightMode1{ background-color: #fefefe !important;} [data-ogsc] .hide-darkmode{ display: none; display: none !important;} [data-ogsc] .show-darkmode{ display: table-row !important; overflow: visible !important; max-height: inherit !important; line-height: auto !important; visibility: inherit !important;} [data-ogsc] .dark-img{ display: block !important; width: auto !important; overflow: visible !important; float: none !important; max-height: inherit !important; max-width: inherit !important; line-height: auto !important; margin-top: 0px !important; visibility: inherit !important;} [data-ogsc] .light-img{ display: none; display: none !important;} [data-ogsc] .nav-headerLinks td, [data-ogsc] .nav-headerLinks td a{ color: #0972D3 !important;} } " }} /><style dangerouslySetInnerHTML={{ __html: "\n    " }} />
        <style media="all" dangerouslySetInnerHTML={{ __html: "\n        html{ -webkit-text-size-adjust: none;} body{ -webkit-font-smoothing: antialiased !important; -webkit-text-size-adjust: none !important; width: 100% !important; height: 100% !important; font-family: 'AmazonEmber', Helvetica, arial, sans-serif;} p, .undoreset div p, .undoreset p{ margin-top: 14px; margin-bottom: 14px;} body, table tr, table td, a, span, table.MsoNormalTable, th{ font-family: 'AmazonEmber', Arial, Helvetica, sans-serif;} .btnLine{ text-decoration: underline !important;} a[x-apple-data-detectors]{ color: inherit !important; font-size: inherit !important; font-family: inherit !important; font-weight: inherit !important; line-height: inherit !important;} a{ outline: none !important;} #email-preview ul{ margin-top: 0px !important; margin-bottom: 0px !important; padding-left: 30px !important;} @media only screen and (max-width: 640px){ .main{ width: 100% !important; min-width: 100% !important;} .deviceWidth1{ width: 90% !important; min-width: 90% !important;} .deviceWidth2{ width: 80% !important; min-width: 80% !important} .block{ display: block !important; margin: 0 auto !important; width: 100% !important;} .center{ text-align: center !important;} .space{ height: 8px;} .imgWidth img{ max-width: 300px;} .full-width img{ width: 100% !important; height: auto !important;} .pad-top{ padding-top: 20px !important;} .left{ text-align: left !important;} .bordernone{ border: none !important;} .deskImg{ display: none !important;} .mobileImg{ display: block !important;} .cta-hide{ display: inline-block !important;} .cta-show{ display: none !important;} .pad-top32{ padding-top: 32px !important;} .height10{ height: 10px !important;} .mob-80{ width: 80px !important; height: auto !important;} .pad-top10{ padding-top: 10px !important;} .border-btm{ border-bottom: 1px solid #B6BEC9 !important; border-right: 0px solid #B6BEC9 !important;} img.mob-widd{ width: 104px !important;} .font-24{ font-size: 24px !important; line-height: 30px !important;} table.logos2-width{ width: 250px !important; min-width: 250px !important;} .logos1-width{ width: 340px !important; min-width: 340px !important;} .pd-lft{ padding-left: 8px;}} @media only screen and (max-width: 425px){ img.mob-widd-d{ width: 88px !important;}}\n    " }} />

      </head>
      <body style={{ "margin-bottom": "0", "-webkit-text-size-adjust": "100%", "padding-bottom": "0", "margin-top": "0", "margin-right": "0", "-ms-text-size-adjust": "100%", "margin-left": "0", "padding-top": "0", "padding-right": "0", "padding-left": "0", "width": "100%" }}>
        {/* OUTER TABLE STARTS */}
        <table className="full-wrap" border={0} cellPadding={0} cellSpacing={0} width="100%" style={{ "border-spacing": "0", "mso-table-lspace": "0pt", "mso-table-rspace": "0pt", "-webkit-text-size-adjust": "100%", "-ms-text-size-adjust": "100%", "border-collapse": "collapse", "margin": "0 auto" }}>
          <tbody>
            <tr>
              {/* Email Body BG Color define */}
              <td className="darkMode" bgcolor="#ffffff" style={{ "background-color": "#ffffff" }}>
                {/* outer width define in this table */}
                <table className="main mktoContainer" id="template-wrapper" cellPadding={0} cellSpacing={0} align="center" border={0} width={680} style={{ "border-collapse": "collapse", "margin": "0 auto", "width": "680px", "min-width": "680px" }}>
                  <tbody>
                    {/* Container Wrap */}
                    {/* 01-banner-module */}
                    {/* 02-heading-module */}
                    {/* 03-logo-banner */}
                    {/* 04-image */}
                    {/* 05-paragraph */}
                    {/* 06-cta */}
                    {/* 07-cta-secondary */}
                    {/* 08-subtitle */}
                    {/* 09-highlights */}
                    {/* 10-icon-highlights */}
                    {/* 11-two-images */}
                    {/* 12-promo-block */}
                    {/* 13-event-details */}
                    {/* 14-featured-session */}
                    {/* 15-event-agenda */}

                    {children}

                    {/* Mkto Module FOOTER MODULE */}
                    {emailType.label === "Marketing" ? (<tr className="mktoModule" id="module-12">
                      <td>
                        <table cellPadding={0} cellSpacing={0} align="center" border={0} width="100%" style={{ "margin": "0 auto", "min-width": "100%" }}>
                          <tbody>
                            <tr>
                              <td className="lightMode" bgcolor="#ffffff" style={{ "background-color": "#ffffff" }}>
                                <table className="deviceWidth1" width={600} cellPadding={0} cellSpacing={0} style={{ "border-spacing": "0", "mso-table-lspace": "0pt", "mso-table-rspace": "0pt", "margin": "0 auto", "width": "600px" }} border={0} align="center">
                                  <tbody>
                                    <tr>
                                      <td height={16} style={{ "font-size": "1px", "line-height": "1px" }}>&nbsp;</td>
                                    </tr>
                                    <tr>
                                      <td className style={{ "vertical-align": "top", "color": "#888888", "font-size": "14px", "line-height": "20px", "-webkit-text-align": "left", "text-align": "left", "font-family": "'AmazonEmber',arial,helvetica", "font-weight": "normal" }}>
                                        <table width="100%" cellSpacing={0} cellPadding={0} border={0}>
                                          <tbody>
                                            <tr>
                                              <td className="dark2">
                                                <div className="mktoSnippet" id="footersnippet1">
                                                  <table width="100%" cellSpacing={0} cellPadding={0} border={0}>
                                                    <tbody>
                                                      <tr>
                                                        <td className="font-family-decor" style={{ "padding": "10px 0px 10px 0px", "border-top": "1px solid #e8e8e8" }}>
                                                          <table style={{ "border-collapse": "collapse" }} width="100%" cellSpacing={0} cellPadding={0} border={0} align="center">
                                                            <tbody>
                                                              <tr>
                                                                <td align="center">
                                                                  <table style={{ "border-collapse": "collapse" }} width="100%" cellSpacing={0} cellPadding={0} border={0} align="center">
                                                                    <tbody>
                                                                      <tr>
                                                                        <td className="font-family-decor" style={{ "font-size": "9px", "color": "#999999", "line-height": "16px" }} align="center"><a href="https://console.aws.amazon.com/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> My Account </a>| <a href="https://aws.amazon.com/getting-started/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> Getting Started</a> | <a href="http://aws.amazon.com/products/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> Products</a> | <a href="http://aws.amazon.com/solutions/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> Solutions</a> | <a href="http://aws.amazon.com/pricing/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> Pricing</a> | <a href="https://aws.amazon.com/partners/work-with-partners/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> Partners</a> | <a href="http://aws.amazon.com/documentation-overview/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> Documentation</a> | <a href="http://aws.amazon.com/training/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> Training</a> | <a href="http://aws.amazon.com/about-aws/events/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> Events &amp; Webinars</a> | <a href="http://aws.amazon.com/activate/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> AWS Activate</a> | <a href="https://aws.amazon.com/marketplace/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank">Marketplace</a> | <a href="http://aws.amazon.com/new/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> What's New</a> | <a href="http://aws.amazon.com/blogs/aws/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank">Blog</a> | <a href="https://aws.amazon.com/resources/analyst-reports/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=&sc_detail=quicklink" target="_blank" style={{ "color": "#146eb4", "-webkit-text-decoration": "none", "text-decoration": "none" }}>Analyst Reports</a><br /><br /><span style={{ "font-size": "11px" }}><strong><a href="https://pages.awscloud.com/index.php/email/emailWebview?mkt_tok=MTEyLVRaTS03NjYAAAGYasZy9_LusORmqlCUSlM3XpVZj_yUHxv-AhScCSdPgkCLB7XhWvK5ASt0pPGd5nbdGtBR4Yskdwn3aW3QiuzpaiLouKsO" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none", "font-size": "11px" }} target="_blank">View Online</a></strong></span></td>
                                                                      </tr>
                                                                    </tbody>
                                                                  </table>
                                                                </td>
                                                              </tr>
                                                            </tbody>
                                                          </table>
                                                        </td>
                                                      </tr>
                                                    </tbody>
                                                  </table>
                                                </div> </td>
                                            </tr>
                                            <tr>
                                              <td height={15} style={{ "font-size": "1px", "line-height": "1px" }}>&nbsp;</td>
                                            </tr>
                                            <tr>
                                              <td className="dark2">
                                                <div className="mktoSnippet" id="footersnippet2">
                                                  <table width="100%" cellSpacing={0} cellPadding={0} border={0}>
                                                    <tbody>
                                                      <tr>
                                                        <td className="social-container" style={{ "padding": "20px 0px 10px", "-webkit-text-align": "center", "text-align": "center" }} width="100%" align="center"><span className="socialPod"> <a href="http://aws.amazon.com/blogs/aws?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" target="_blank" title="AWS Blog"><img src="https://pages.awscloud.com/rs/112-TZM-766/images/Blog_GL_Social_2021.png" alt="AWS Blog" width={25} height={25} border={0} constrain="true" imagepreview="false" /></a>&nbsp;</span>&nbsp;<span className="socialPod">&nbsp;&nbsp;<a href="http://twitter.com/awscloud" target="_blank" title="AWS on X"><img src="https://pages.awscloud.com/rs/112-TZM-766/images/x-logo-black-2.png?version=0" alt="AWS on 
                          X" width={25} height={25} border={0} constrain="true" imagepreview="false" /></a></span>&nbsp;<span className="socialPod">&nbsp;<a href="http://www.facebook.com/amazonwebservices" target="_blank" title="AWS on Facebook"><img src="https://pages.awscloud.com/rs/112-TZM-766/images/Facebook_GL_Social_2021.png" alt="AWS on Facebook" width={25} height={25} border={0} constrain="true" imagepreview="false" /></a>&nbsp; &nbsp;&nbsp;<a href="https://www.twitch.tv/aws" target="_blank" title="AWS on Twitch"><img src="https://pages.awscloud.com/rs/112-TZM-766/images/Twitch_GL_Social_2021.png" alt="AWS on Twitch" width={25} height={25} constrain="true" imagepreview="false" /></a></span>&nbsp; &nbsp;<span className="socialPod"> <a href="http://www.youtube.com/user/AmazonWebServices" target="_blank" title="AWS on YouTube"><img src="https://pages.awscloud.com/rs/112-TZM-766/images/YouTube_GL_Social_2021.png" alt="AWS on YouTube" width={25} height={25} border={0} constrain="true" imagepreview="false" /></a></span>&nbsp; &nbsp;<span className="socialPod"> <a href="https://aws.amazon.com/podcasts/aws-podcast/" target="_blank" title="AWS Podcast"><img src="https://pages.awscloud.com/rs/112-TZM-766/images/Podcast_GL_Social_2021.png" alt="AWS Podcast" width={25} height={25} border={0} constrain="true" imagepreview="false" /></a> </span></td>
                                                      </tr>
                                                    </tbody>
                                                  </table>
                                                </div> </td>
                                            </tr>
                                            <tr>
                                              <td height={15} style={{ "font-size": "1px", "line-height": "1px" }}>&nbsp;</td>
                                            </tr>
                                            <tr>
                                              <td className="dark2">
                                                <div className="mktoSnippet" id="footersnippet3">
                                                  <div className="mktEditable" id="Footer">
                                                    <table style={{ "max-width": "600px" }} className="responsive-table" width="100%" cellSpacing={0} cellPadding={0} border={0} align="center">
                                                      <tbody>
                                                        <tr>
                                                          <td className="font-family-decor" style={{ "font-size": "12px", "line-height": "18px", "color": "#666666" }} align="center">
                                                            <dl>
                                                              <dt style={{ "color": "#666666", "font-size": "10px", "-webkit-text-align": "center", "text-align": "center" }}>If you'd rather not receive future emails from Amazon Web Services, <a href="https://pages.awscloud.com/communication-preferences.html?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=&param=unsubscribe&mkt_unsubscribe=1" style={{ "-ms-text-size-adjust": "100%", "-webkit-text-size-adjust": "100%", "color": "#007eb9", "font-size": "10px", "-webkit-text-decoration": "none", "text-decoration": "none" }}>unsubscribe here</a>.</dt>
                                                              <dt style={{ "color": "#666666", "font-size": "10px", "-webkit-text-align": "center", "text-align": "center" }}>Amazon Web Services, Inc. is a subsidiary of Amazon.com, Inc. Amazon.com is a registered trademark of Amazon.com.<br /></dt>
                                                              <dt style={{ "color": "#666666", "font-size": "10px", "-webkit-text-align": "center", "text-align": "center" }}>This message was produced and distributed by Amazon Web Services, Inc. or its <a href="https://aws.amazon.com/legal/marketingentities/" style={{ "-ms-text-size-adjust": "100%", "-webkit-text-size-adjust": "100%", "color": "#007eb9", "font-size": "10px", "-webkit-text-decoration": "none", "text-decoration": "none" }}>affiliates</a>, 410 Terry Ave. North, Seattle, WA 98109.<br /></dt>
                                                              <dt style={{ "color": "#666666", "font-size": "10px", "-webkit-text-align": "center", "text-align": "center" }}>©2024, Amazon Web Services, Inc. or its affiliates. All rights reserved. Read our <a href="https://aws.amazon.com/privacy/" style={{ "-ms-text-size-adjust": "100%", "-webkit-text-size-adjust": "100%", "color": "#007eb9", "font-size": "10px", "-webkit-text-decoration": "none", "text-decoration": "none" }}>Privacy Notice</a>. Ref: 116826</dt>
                                                            </dl>
                                                          </td>
                                                        </tr>
                                                      </tbody>
                                                    </table>
                                                  </div>
                                                </div> </td>
                                            </tr>
                                          </tbody>
                                        </table> </td>
                                    </tr>
                                    <tr>
                                      <td height={20} style={{ "font-size": "1px", "line-height": "1px" }}>&nbsp;</td>
                                    </tr>
                                  </tbody>
                                </table> </td>
                            </tr>
                          </tbody>
                        </table> </td>
                    </tr>) : (<table cellPadding={0} cellSpacing={0} align="center" border={0} width="100%" style={{ "margin": "0 auto", "min-width": "100%" }}>
                      <tbody>
                        <tr>
                          <td className="lightMode" bgcolor="#ffffff" style={{ "background-color": "#ffffff" }}>
                            <table className="deviceWidth1" width={600} cellPadding={0} cellSpacing={0} style={{ "border-spacing": "0", "mso-table-lspace": "0pt", "mso-table-rspace": "0pt", "margin": "0 auto", "width": "600px" }} border={0} align="center">
                              <tbody>
                                <tr>
                                  <td height={16} style={{ "font-size": "1px", "line-height": "1px" }}>&nbsp;</td>
                                </tr>
                                <tr>
                                  <td className style={{ "vertical-align": "top", "color": "#888888", "font-size": "14px", "line-height": "20px", "-webkit-text-align": "left", "text-align": "left", "font-family": "'AmazonEmber',arial,helvetica", "font-weight": "normal" }}>
                                    <table width="100%" cellSpacing={0} cellPadding={0} border={0}>
                                      <tbody>
                                        <tr>
                                          <td className="dark2">
                                            <div className="mktoSnippet" id="footersnippet1">
                                              <table width="100%" cellSpacing={0} cellPadding={0} border={0}>
                                                <tbody>
                                                  <tr>
                                                    <td className="font-family-decor" style={{ "padding": "10px 0px 10px 0px", "border-top": "1px solid #e8e8e8" }}>
                                                      <table style={{ "border-collapse": "collapse" }} width="100%" cellSpacing={0} cellPadding={0} border={0} align="center">
                                                        <tbody>
                                                          <tr>
                                                            <td align="center">
                                                              <table style={{ "border-collapse": "collapse" }} width="100%" cellSpacing={0} cellPadding={0} border={0} align="center">
                                                                <tbody>
                                                                  <tr>
                                                                    <td className="font-family-decor" style={{ "font-size": "9px", "color": "#999999", "line-height": "16px" }} align="center"><a href="https://console.aws.amazon.com/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> My Account </a>| <a href="https://aws.amazon.com/getting-started/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> Getting Started</a> | <a href="http://aws.amazon.com/products/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> Products</a> | <a href="http://aws.amazon.com/solutions/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> Solutions</a> | <a href="http://aws.amazon.com/pricing/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> Pricing</a> | <a href="https://aws.amazon.com/partners/work-with-partners/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> Partners</a> | <a href="http://aws.amazon.com/documentation-overview/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> Documentation</a> | <a href="http://aws.amazon.com/training/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> Training</a> | <a href="http://aws.amazon.com/about-aws/events/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> Events &amp; Webinars</a> | <a href="http://aws.amazon.com/activate/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> AWS Activate</a> | <a href="https://aws.amazon.com/marketplace/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank">Marketplace</a> | <a href="http://aws.amazon.com/new/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank"> What's New</a> | <a href="http://aws.amazon.com/blogs/aws/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none" }} target="_blank">Blog</a> | <a href="https://aws.amazon.com/resources/analyst-reports/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=&sc_detail=quicklink" target="_blank" style={{ "color": "#146eb4", "-webkit-text-decoration": "none", "text-decoration": "none" }}>Analyst Reports</a><br /><br /><span style={{ "font-size": "11px" }}><strong><a href="https://pages.awscloud.com/index.php/email/emailWebview?mkt_tok=MTEyLVRaTS03NjYAAAGYatJpeVXU09aRxTFDjHOc005JgDqY18ZYi92Yn_Mn0dMOWdjwk0D8Via8MzdwdLZE556vH7-2QNony5nWXAMOImRE6_IV" style={{ "color": "#007eb9", "-webkit-text-decoration": "none", "text-decoration": "none", "font-size": "11px" }} target="_blank">View Online</a></strong></span></td>
                                                                  </tr>
                                                                </tbody>
                                                              </table>
                                                            </td>
                                                          </tr>
                                                        </tbody>
                                                      </table>
                                                    </td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                            </div> </td>
                                        </tr>
                                        <tr>
                                          <td height={15} style={{ "font-size": "1px", "line-height": "1px" }}>&nbsp;</td>
                                        </tr>
                                        <tr>
                                          <td className="dark2">
                                            <div className="mktoSnippet" id="footersnippet2">
                                              <table width="100%" cellSpacing={0} cellPadding={0} border={0}>
                                                <tbody>
                                                  <tr>
                                                    <td className="social-container" style={{ "padding": "20px 0px 10px", "-webkit-text-align": "center", "text-align": "center" }} width="100%" align="center"><span className="socialPod"> <a href="http://aws.amazon.com/blogs/aws?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=loc&sc_country=&sc_region=" target="_blank" title="AWS Blog"><img src="https://pages.awscloud.com/rs/112-TZM-766/images/Blog_GL_Social_2021.png" alt="AWS Blog" width={25} height={25} border={0} constrain="true" imagepreview="false" /></a>&nbsp;</span>&nbsp;<span className="socialPod">&nbsp;&nbsp;<a href="http://twitter.com/awscloud" target="_blank" title="AWS on X"><img src="https://pages.awscloud.com/rs/112-TZM-766/images/x-logo-black-2.png?version=0" alt="AWS on 
X" width={25} height={25} border={0} constrain="true" imagepreview="false" /></a></span>&nbsp;<span className="socialPod">&nbsp;<a href="http://www.facebook.com/amazonwebservices" target="_blank" title="AWS on Facebook"><img src="https://pages.awscloud.com/rs/112-TZM-766/images/Facebook_GL_Social_2021.png" alt="AWS on Facebook" width={25} height={25} border={0} constrain="true" imagepreview="false" /></a>&nbsp; &nbsp;&nbsp;<a href="https://www.twitch.tv/aws" target="_blank" title="AWS on Twitch"><img src="https://pages.awscloud.com/rs/112-TZM-766/images/Twitch_GL_Social_2021.png" alt="AWS on Twitch" width={25} height={25} constrain="true" imagepreview="false" /></a></span>&nbsp; &nbsp;<span className="socialPod"> <a href="http://www.youtube.com/user/AmazonWebServices" target="_blank" title="AWS on YouTube"><img src="https://pages.awscloud.com/rs/112-TZM-766/images/YouTube_GL_Social_2021.png" alt="AWS on YouTube" width={25} height={25} border={0} constrain="true" imagepreview="false" /></a></span>&nbsp; &nbsp;<span className="socialPod"> <a href="https://aws.amazon.com/podcasts/aws-podcast/" target="_blank" title="AWS Podcast"><img src="https://pages.awscloud.com/rs/112-TZM-766/images/Podcast_GL_Social_2021.png" alt="AWS Podcast" width={25} height={25} border={0} constrain="true" imagepreview="false" /></a> </span></td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                            </div> </td>
                                        </tr>
                                        <tr>
                                          <td height={15} style={{ "font-size": "1px", "line-height": "1px" }}>&nbsp;</td>
                                        </tr>
                                        <tr>
                                          <td className="dark2">
                                            <div className="mktoSnippet" id="footersnippet3">
                                              <div className="mktEditable" id="Footer">
                                                <table style={{ "max-width": "600px" }} className="responsive-table" width="100%" cellSpacing={0} cellPadding={0} border={0} align="center">
                                                  <tbody>
                                                    <tr>
                                                      <td className="font-family-decor" style={{ "font-size": "12px", "line-height": "18px", "color": "#666666" }} align="center">
                                                        <dl>
                                                          <dt style={{ "color": "#666666", "font-size": "10px", "-webkit-text-align": "center", "text-align": "center" }}>
                                                            Amazon Web Services, Inc. is a subsidiary of Amazon.com, Inc. Amazon.com is a registered trademark of Amazon.com.
                                                          </dt>
                                                          <dt style={{ "color": "#666666", "font-size": "10px", "-webkit-text-align": "center", "text-align": "center" }}>
                                                            This message was produced and distributed by Amazon Web Services, Inc. or its
                                                            <a href="https://aws.amazon.com/legal/marketingentities/" style={{ "-ms-text-size-adjust": "100%", "-webkit-text-size-adjust": "100%", "color": "#007eb9", "font-size": "10px", "-webkit-text-decoration": "none", "text-decoration": "none" }}>affiliates</a>, 410 Terry Ave. North, Seattle, WA 98109.
                                                          </dt>
                                                          <dt style={{ "color": "#666666", "font-size": "10px", "-webkit-text-align": "center", "text-align": "center" }}>
                                                            © 2024, Amazon Web Services, Inc. or its affiliates. All rights reserved. Read our
                                                            <a href="https://aws.amazon.com/privacy/" style={{ "-ms-text-size-adjust": "100%", "-webkit-text-size-adjust": "100%", "color": "#007eb9", "font-size": "10px", "-webkit-text-decoration": "none", "text-decoration": "none" }}>Privacy Notice</a>. Ref: 116826
                                                          </dt>
                                                        </dl> </td>
                                                    </tr>
                                                  </tbody>
                                                </table>
                                              </div>
                                            </div> </td>
                                        </tr>
                                      </tbody>
                                    </table> </td>
                                </tr>
                                <tr>
                                  <td height={20} style={{ "font-size": "1px", "line-height": "1px" }}>&nbsp;</td>
                                </tr>
                              </tbody>
                            </table> </td>
                        </tr>
                      </tbody>
                    </table>)}

                    {/* End Footer Module */}
                    {/* Container Wrap */}
                  </tbody>
                </table>
              </td>
            </tr>
          </tbody>
        </table>
      </body>
    </html>
  );
};

export default EmailShell;
