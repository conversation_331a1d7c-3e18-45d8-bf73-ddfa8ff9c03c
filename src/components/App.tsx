// @ts-nocheck
import React, { useEffect, useState } from 'react';
import { Route, Routes } from 'react-router-dom';
import { useStore } from '../data/store';
import { CampaignsApiEndpoint } from '../config';

import ServiceHomepage from './home';

// Class App is the "output" generated on every build,
// it is what you will see on the webpage.
export default function App() {
  const [isLoading, setIsLoading] = useState(false);
  const [isCurrentUserOwner, setIsCurrentUserOwner] = useState(true);
  const mapModulesToHTML = useStore(state => state.mapModulesToHTML);
  const setSelectedCampaignId = useStore(state => state.setSelectedCampaignId);
  const setEmailData = useStore(state => state.setEmailData);

  useEffect(() => {
    // Function to parse query parameters
    const getQueryParams = () => {
      const params = new URLSearchParams(window.location.search);
      const emailId = params.get('emailId');

      return { emailId };
    };

    // Function to load email by ID
    const loadEmailFromParams = async () => {
      const { emailId } = getQueryParams();

      if (emailId) {
        setIsLoading(true);

        try {
          // API Configuration
          const API_KEY = 'nutbLu8oMw6w9aVuw5h811QzVsSeWwJt3dJvwMX5';
          const headers = {
            'x-api-key': API_KEY,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          };

          // Fetch the email
          const response = await window.fetch(`${CampaignsApiEndpoint}/emails/${emailId}`, {
            method: 'GET',
            // headers: headers
          });

          if (response.ok) {
            const data = await response.json();

            if (data.body && data.body.data) {
              // Get current user
              const user = window.harmony.api.getUser();
              const currentUserLogin = user.login;

              // Check if current user is the owner
              const isOwner = data.body.data.emailOwner === currentUserLogin;
              setIsCurrentUserOwner(isOwner);

              // Store the campaign ID
              setSelectedCampaignId(emailId);

              // Set email data
              setEmailData({
                emailName: data.body.data.emailName || '',
                emailOwner: data.body.data.emailOwner || ''
              });

              // Map the email data to the UI
              if (data.body.data.emailData) {
                mapModulesToHTML(data.body.data.emailData);
              }

              console.log('Email loaded from URL parameters:', data.body.data);
            }
          }
        } catch (error) {
          console.error('Error loading email from URL parameters:', error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    // Load email if parameters are present
    loadEmailFromParams();
  }, [mapModulesToHTML, setSelectedCampaignId, setEmailData]);

  // If loading, you could return a loading component here
  // or pass the loading state to ServiceHomepage

  return (
    <Routes>
      <Route path="/" element={<ServiceHomepage isLoading={isLoading} isCurrentUserOwner={isCurrentUserOwner} />} />
    </Routes>
  );
}
