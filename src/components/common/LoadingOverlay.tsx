import React from 'react';
import Spinner from '@amzn/awsui-components-react/polaris/spinner';

interface LoadingOverlayProps {
  message?: string;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ message = 'Loading...' }) => (
  <div 
    style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 9999
    }}
  >
    <Spinner size="large" />
    <div style={{ marginTop: '20px', fontSize: '16px', fontWeight: 'bold' }}>
      {message}
    </div>
  </div>
);

export default LoadingOverlay;