// Types for email-related components

export interface Metadata {
  subject: {
    label: string;
    value: string;
    maxLen: number;
  };
  preheader: {
    label: string;
    value: string;
    maxLen: number;
  };
  trk: {
    label: string;
    value: string;
    maxLen: number;
  };
  sender: string;
  OPER?: boolean;
  variant?: string;
  RTL?: boolean;
}

export interface Module {
  id: string;
  copy: number;
  order: number;
  label: string;
  module_type?: string;
  selected: boolean;
  inputs: any[];
  html: string | (() => string);
}

export interface Template {
  modules: Module[];
  metadata: Metadata;
}

export interface EmailData {
  emailName: string;
  emailOwner: string;
  campaignId?: string;
  createdAt?: string;
  updatedAt?: string;
  emailData?: {
    metadata: Metadata;
    modules: Module[];
  };
}

export interface APIResponse {
  body: EmailData;
  data: EmailData;
  emailOwner?: string;
  emailName?: string;
  emailData?: EmailData;
  campaignId?: string;
  createdAt?: string;
  updatedAt?: string;
  lastModified?: string;
  emailType?: string;
  variant?: string;
}

export interface RightPanelProps {
  // Add props if needed
}