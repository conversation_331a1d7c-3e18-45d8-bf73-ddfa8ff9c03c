// @ts-nocheck
// HeadingModule.tsx
import React from 'react';

interface HeadingModuleProps {
    moduleId?: string;
    headingText?: string;
}

export const HeadingModule: React.FC<HeadingModuleProps> = ({
    moduleId = "module-03527ea3ae-ab07-46a1-b872-916b69342362",
    headingText = "This is an email heading"
}) => {
    return (
        <tr className="mktoModule" id={moduleId}>
            <td>
                <table
                    width="100%"
                    cellPadding="0"
                    cellSpacing="0"
                    style={{
                        borderSpacing: 0,
                        msoTableLspace: '0pt',
                        msoTableRspace: '0pt',
                        margin: '0 auto',
                        width: '100%'
                    }}
                    border={0}
                    align="center"
                >
                    <tbody>
                        <tr>
                            <td
                                className="lightMode"
                                style={{ backgroundColor: '#ffffff' }}
                                bgcolor="#ffffff"
                            >
                                <table
                                    className="deviceWidth1"
                                    width={600}
                                    cellPadding="0"
                                    cellSpacing="0"
                                    style={{
                                        borderSpacing: 0,
                                        msoTableLspace: '0pt',
                                        msoTableRspace: '0pt',
                                        margin: '0 auto',
                                        width: '600px'
                                    }}
                                    border={0}
                                    align="center"
                                >
                                    <tbody>
                                        <tr>
                                            <td height={0} style={{ lineHeight: '0px', fontSize: '0px' }}></td>
                                        </tr>
                                        <tr>
                                            <td
                                                className="dark"
                                                valign="top"
                                                style={{
                                                    fontFamily: 'AmazonEmber,arial, Helvetica, sans-serif',
                                                    fontSize: '16px',
                                                    lineHeight: '24px',
                                                    fontWeight: 'normal',
                                                    color: '#232B37',
                                                    textAlign: 'left',
                                                    msoLineHeightRule: 'exactly'
                                                }}
                                            >
                                                <div>
                                                    <table
                                                        className="main"
                                                        width={680}
                                                        cellPadding="0"
                                                        cellSpacing="0"
                                                        style={{
                                                            borderSpacing: 0,
                                                            msoTableLspace: '0pt',
                                                            msoTableRspace: '0pt',
                                                            margin: '0 auto',
                                                            width: '680px'
                                                        }}
                                                        border={0}
                                                        align="center"
                                                    >
                                                        <tbody>
                                                            <tr>
                                                                <td
                                                                    className="lightMode"
                                                                    bgcolor="#ffffff"
                                                                    style={{ backgroundColor: '#ffffff' }}
                                                                >
                                                                    <table
                                                                        className="deviceWidth1"
                                                                        width={600}
                                                                        align="center"
                                                                        border={0}
                                                                        cellPadding="0"
                                                                        cellSpacing="0"
                                                                        style={{
                                                                            width: '600px',
                                                                            margin: '0 auto',
                                                                            borderSpacing: 0
                                                                        }}
                                                                    >
                                                                        <tbody>
                                                                            <tr>
                                                                                <td
                                                                                    className="dark center font-24"
                                                                                    style={{
                                                                                        textAlign: 'center',
                                                                                        fontFamily: "'AmazonEmber', Helvetica, arial, sans-serif",
                                                                                        fontSize: '36px',
                                                                                        lineHeight: '44px',
                                                                                        color: '#161D26',
                                                                                        msoLineHeightRule: 'exactly',
                                                                                        fontWeight: 'bold'
                                                                                    }}
                                                                                >
                                                                                    <div>{headingText}</div>
                                                                                </td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td
                                                                    className="lightMode"
                                                                    bgcolor="#ffffff"
                                                                    height={0}
                                                                    style={{
                                                                        backgroundColor: '#ffffff',
                                                                        lineHeight: '1px',
                                                                        fontSize: '1px'
                                                                    }}
                                                                >
                                                                    &nbsp;
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td height={40} style={{ lineHeight: '1px', fontSize: '1px' }}>
                                                &nbsp;
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    );
};

export default HeadingModule;
