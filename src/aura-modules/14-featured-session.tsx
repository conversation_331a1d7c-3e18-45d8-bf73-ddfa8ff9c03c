// @ts-nocheck
import React from 'react';

interface FeaturedSessionProps {
    imageUrl: string;
    imageAlt: string;
    title: string;
    description: string;
}

export const FeaturedSession: React.FC<FeaturedSessionProps> = ({
    imageUrl = "https://pages.awscloud.com/rs/112-TZM-766/images/Innovate_FEA22_Mat-Guimaraes.png",
    imageAlt = "Featured Speaker",
    title = "Session title 1",
    description = "Session details to be added"
}) => {
    return (
        <div style={{ maxWidth: "600px", margin: "0 auto" }} className="mktoModule" id="module-03527ea3ae-ab07-46a1-b872-916b69342362">
            <td>
                <table width="100%" cellPadding={0} cellSpacing={0}
                    style={{ borderSpacing: 0, msoTableLspace: '0pt', msoTableRspace: '0pt', margin: '0 auto', width: '100%' }}
                    border={0} align="center">
                    <tbody>
                        <tr>
                            <td className="lightMode" style={{ backgroundColor: '#ffffff' }} bgcolor="#ffffff">
                                <table className="deviceWidth1" width={600} cellPadding={0} cellSpacing={0}
                                    style={{ borderSpacing: 0, msoTableLspace: '0pt', msoTableRspace: '0pt', margin: '0 auto', width: '600px' }}
                                    border={0} align="center">
                                    <tbody>
                                        <tr>
                                            <td height={0} style={{ lineHeight: '0px', fontSize: '0px' }}></td>
                                        </tr>
                                        <tr>
                                            <td className="dark" valign="top"
                                                style={{
                                                    fontFamily: 'AmazonEmber,arial, Helvetica, sans-serif', fontSize: '16px',
                                                    lineHeight: '24px', fontWeight: 'normal', color: '#232B37',
                                                    textAlign: 'left', msoLineHeightRule: 'exactly'
                                                }}>
                                                <div>
                                                    <table align="center" width="100%" cellPadding={0} cellSpacing={0} border={0}>
                                                        <tbody>
                                                            <tr>
                                                                <th className="block deviceWidth" width={120} valign="top">
                                                                    <table align="center" width="100%" cellPadding={0} cellSpacing={0} border={0}>
                                                                        <tbody>
                                                                            <tr>
                                                                                <td className="full-width" align="center">
                                                                                    <img src={imageUrl}
                                                                                        width={120}
                                                                                        style={{ maxWidth: '120px' }}

                                                                                        align="left" />
                                                                                </td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </th>
                                                                <th className="block" width={30} height={20}
                                                                    style={{ fontSize: '1px', lineHeight: '1px' }}>&nbsp;</th>
                                                                <th className="block deviceWidth" width={408} valign="middle"
                                                                    style={{ textAlign: 'left', fontWeight: 400 }}>
                                                                    <table align="center" width="100%" cellPadding={0} cellSpacing={0} border={0}>
                                                                        <tbody>
                                                                            <tr>
                                                                                <td valign="middle"
                                                                                    style={{
                                                                                        verticalAlign: 'top', textAlign: 'left',
                                                                                        fontSize: '16px', lineHeight: '24px',
                                                                                        fontFamily: 'AmazonEmber, arial, helvetica',
                                                                                        fontWeight: 'normal'
                                                                                    }}
                                                                                    className="mobile-center">
                                                                                    <table width="100%" cellPadding={0} cellSpacing={0}
                                                                                        style={{
                                                                                            borderSpacing: 0, msoTableLspace: '0pt',
                                                                                            msoTableRspace: '0pt', margin: '0 auto',
                                                                                            width: '100%'
                                                                                        }}
                                                                                        border={0} align="center">
                                                                                        <tbody>
                                                                                            <tr>
                                                                                                <td className="dark" align="left"
                                                                                                    style={{
                                                                                                        fontFamily: 'AmazonEmber, Helvetica, arial, sans-serif',
                                                                                                        fontSize: '20px', lineHeight: '24px',
                                                                                                        color: '#161d26', fontWeight: 600,
                                                                                                        msoLineHeightRule: 'exactly'
                                                                                                    }}>
                                                                                                    <div>
                                                                                                        {title}
                                                                                                    </div>
                                                                                                </td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <td height={8}
                                                                                                    style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <td className="dark" valign="top"
                                                                                                    style={{
                                                                                                        fontFamily: 'AmazonEmber, Helvetica, arial, sans-serif',
                                                                                                        fontSize: '16px', lineHeight: '24px',
                                                                                                        fontWeight: 400, color: '#232b37',
                                                                                                        textAlign: 'left',
                                                                                                        msoLineHeightRule: 'exactly'
                                                                                                    }}>
                                                                                                    <div>
                                                                                                        {description}
                                                                                                    </div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </tbody>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </th>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td height={40} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </div>
    );
};

export default FeaturedSession;
