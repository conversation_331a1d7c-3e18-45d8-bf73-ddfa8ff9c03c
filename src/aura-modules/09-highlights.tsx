// @ts-nocheck
// HighlightsModule.tsx
import React from 'react';
import parse from "html-react-parser";

interface HighlightItem {
    number: string;
    title: string;
    description: string;
}

interface HighlightsModuleProps {
    highlights: HighlightItem[];
}

export const HighlightsModule: React.FC<HighlightsModuleProps> = ({
    highlights
}) => {
    return (
        <div style={{ maxWidth: "600px", margin: "0 auto" }} className="mktoModule" id="module-03527ea3ae-ab07-46a1-b872-916b69342362">
            <td>
                <table
                    width="100%"
                    cellPadding="0"
                    cellSpacing="0"
                    style={{
                        borderSpacing: 0,
                        msoTableLspace: '0pt',
                        msoTableRspace: '0pt',
                        margin: '0 auto',
                        width: '100%'
                    }}
                    border={0}
                    align="center"
                >
                    <tbody>
                        <tr>
                            <td
                                className="lightMode"
                                style={{ backgroundColor: '#ffffff' }}
                                bgcolor="#ffffff"
                            >
                                <table
                                    className="deviceWidth1"
                                    width={600}
                                    cellPadding="0"
                                    cellSpacing="0"
                                    style={{
                                        borderSpacing: 0,
                                        msoTableLspace: '0pt',
                                        msoTableRspace: '0pt',
                                        margin: '0 auto',
                                        width: '600px'
                                    }}
                                    border={0}
                                    align="center"
                                >
                                    <tbody>
                                        <tr>
                                            <td height={0} style={{ lineHeight: '0px', fontSize: '0px' }}></td>
                                        </tr>
                                        <tr>
                                            <td
                                                className="dark"
                                                valign="top"
                                                style={{
                                                    fontFamily: 'AmazonEmber,arial, Helvetica, sans-serif',
                                                    fontSize: '16px',
                                                    lineHeight: '24px',
                                                    fontWeight: 'normal',
                                                    color: '#232B37',
                                                    textAlign: 'left',
                                                    msoLineHeightRule: 'exactly'
                                                }}
                                            >
                                                <div>
                                                    {/* Content Module Before Wrap */}
                                                    <table
                                                        style={{
                                                            borderSpacing: 0,
                                                            msoTableLspace: '0pt',
                                                            msoTableRspace: '0pt',
                                                            borderCollapse: 'collapse'
                                                        }}
                                                        align="center"
                                                        border={0}
                                                        cellPadding="0"
                                                        cellSpacing="0"
                                                        width="100%"
                                                    >
                                                        <tbody>
                                                            {highlights.map((highlight, index) => (
                                                                <React.Fragment key={index}>
                                                                    <tr>
                                                                        <td>
                                                                            <table
                                                                                width="100%"
                                                                                cellPadding="0"
                                                                                cellSpacing="0"
                                                                                style={{
                                                                                    borderSpacing: 0,
                                                                                    msoTableLspace: '0pt',
                                                                                    msoTableRspace: '0pt',
                                                                                    margin: '0 auto',
                                                                                    width: '100%'
                                                                                }}
                                                                                border={0}
                                                                                align="center"
                                                                            >
                                                                                <tbody>
                                                                                    <tr>
                                                                                        <td
                                                                                            className="dark"
                                                                                            valign="top"
                                                                                            width="24"
                                                                                            align="center"
                                                                                            style={{
                                                                                                fontFamily: "'AmazonEmber', Helvetica, arial, sans-serif",
                                                                                                color: '#161D26',
                                                                                                fontWeight: 700,
                                                                                                msoLineHeightRule: 'exactly',
                                                                                                width: '24px',
                                                                                                maxWidth: '24px',
                                                                                                minWidth: '24px',
                                                                                                textTransform: 'uppercase',
                                                                                                wordBreak: 'break-word'
                                                                                            }}
                                                                                        >
                                                                                            <div style={{
                                                                                                fontWeight: 700,
                                                                                                fontSize: '40px',
                                                                                                lineHeight: '44px'
                                                                                            }}>
                                                                                                {index + 1}
                                                                                            </div>
                                                                                        </td>
                                                                                        <td
                                                                                            className="dateRight"
                                                                                            width="40"
                                                                                            style={{
                                                                                                width: '40px',
                                                                                                fontSize: '1px',
                                                                                                lineHeight: '1px'
                                                                                            }}
                                                                                        >
                                                                                            &nbsp;
                                                                                        </td>
                                                                                        <td
                                                                                            valign="top"
                                                                                            width="536"
                                                                                            style={{ width: '536px' }}
                                                                                        >
                                                                                            <table
                                                                                                width="100%"
                                                                                                cellPadding="0"
                                                                                                cellSpacing="0"
                                                                                                style={{
                                                                                                    borderSpacing: 0,
                                                                                                    msoTableLspace: '0pt',
                                                                                                    msoTableRspace: '0pt',
                                                                                                    margin: '0 auto',
                                                                                                    width: '100%'
                                                                                                }}
                                                                                                border={0}
                                                                                                align="center"
                                                                                            >
                                                                                                <tbody>
                                                                                                    <tr>
                                                                                                        <td
                                                                                                            className="dark"
                                                                                                            align="left"
                                                                                                            style={{
                                                                                                                fontFamily: "'AmazonEmber', Helvetica, arial, sans-serif",
                                                                                                                fontSize: '20px',
                                                                                                                lineHeight: '24px',
                                                                                                                color: '#161D26',
                                                                                                                fontWeight: 600,
                                                                                                                msoLineHeightRule: 'exactly'
                                                                                                            }}
                                                                                                        >
                                                                                                            <div>{highlight.title}</div>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    <tr>
                                                                                                        <td
                                                                                                            height="8"
                                                                                                            style={{
                                                                                                                lineHeight: '1px',
                                                                                                                fontSize: '1px'
                                                                                                            }}
                                                                                                        >
                                                                                                            &nbsp;
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    <tr>
                                                                                                        <td
                                                                                                            className="dark"
                                                                                                            valign="top"
                                                                                                            style={{
                                                                                                                fontFamily: "'AmazonEmber', Helvetica, arial, sans-serif",
                                                                                                                fontSize: '16px',
                                                                                                                lineHeight: '24px',
                                                                                                                fontWeight: 400,
                                                                                                                color: '#232B37',
                                                                                                                textAlign: 'left',
                                                                                                                msoLineHeightRule: 'exactly'
                                                                                                            }}
                                                                                                        >
                                                                                                            <div>{parse(highlight.description)}</div>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                </tbody>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </tbody>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                    {index < highlights.length - 1 && (
                                                                        <tr>
                                                                            <td
                                                                                height="24"
                                                                                style={{
                                                                                    lineHeight: '1px',
                                                                                    fontSize: '1px'
                                                                                }}
                                                                            >
                                                                                &nbsp;
                                                                            </td>
                                                                        </tr>
                                                                    )}
                                                                </React.Fragment>
                                                            ))}
                                                        </tbody>
                                                    </table>
                                                    {/* Content Module After Wrap */}
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td height={40} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </div>
    );
};

export default HighlightsModule;
