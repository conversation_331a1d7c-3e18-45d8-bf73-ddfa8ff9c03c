// @ts-nocheck
// BannerModule.tsx
import React from 'react';

interface BannerModuleProps {
    moduleId?: string;
}

export const BannerModule: React.FC<BannerModuleProps> = ({ moduleId = "module-01", backgroundSrc }) => {
    return (
        <tr className="mktoModule" id={moduleId}>
            <td>
                <table
                    width="100%"
                    cellPadding="0"
                    cellSpacing="0"
                    style={{
                        borderSpacing: 0,
                        msoTableLspace: '0pt',
                        msoTableRspace: '0pt',
                        margin: '0 auto',
                        width: '100%'
                    }}
                    border={0}
                    align="center"
                >
                    <tbody>
                        <tr>
                            <td
                                className="lightMode"
                                style={{ backgroundColor: '#ffffff' }}
                                bgcolor="#ffffff"
                            >
                                <table
                                    className="deviceWidth1"
                                    width={600}
                                    cellPadding="0"
                                    cellSpacing="0"
                                    style={{
                                        borderSpacing: 0,
                                        msoTableLspace: '0pt',
                                        msoTableRspace: '0pt',
                                        margin: '0 auto',
                                        width: '600px'
                                    }}
                                    border={0}
                                    align="center"
                                >
                                    <tbody>
                                        <tr>
                                            <td height={0} style={{ lineHeight: '0px', fontSize: '0px' }}></td>
                                        </tr>
                                        <tr>
                                            <td
                                                className="dark"
                                                valign="top"
                                                style={{
                                                    fontFamily: 'AmazonEmber,arial, Helvetica, sans-serif',
                                                    fontSize: '16px',
                                                    lineHeight: '24px',
                                                    fontWeight: 'normal',
                                                    color: '#232B37',
                                                    textAlign: 'left',
                                                    msoLineHeightRule: 'exactly'
                                                }}
                                            >
                                                <div>
                                                    {/* Content Module Before Wrap */}
                                                    <table
                                                        className="main"
                                                        width={680}
                                                        cellPadding="0"
                                                        cellSpacing="0"
                                                        style={{
                                                            borderSpacing: 0,
                                                            msoTableLspace: '0pt',
                                                            msoTableRspace: '0pt',
                                                            margin: '0 auto',
                                                            width: '680px'
                                                        }}
                                                        border={0}
                                                        align="center"
                                                    >
                                                        <tbody>
                                                            <tr>
                                                                <td
                                                                    className="lightMode1"
                                                                    background={backgroundSrc}
                                                                    bgcolor="#ffffff"
                                                                    style={{
                                                                        backgroundImage: `url(${backgroundSrc})`,
                                                                        backgroundRepeat: 'no-repeat',
                                                                        backgroundPosition: 'top',
                                                                        backgroundSize: 'auto',
                                                                        backgroundColor: '#ffffff'
                                                                    }}
                                                                >
                                                                    {/* MS Outlook specific code */}
                                                                    {/* {`<!--[if gte mso 9]>
                                    <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width:680px;display:block;height:78px;">
                                      <v:fill type="frame" src="https://pages.awscloud.com/rs/112-TZM-766/images/IImg-Gradient_A.jpg" color="#ffffff" />
                                      <v:textbox inset="0,0,0,0">
                                  <![endif]-->`} */}

                                                                    <table
                                                                        className="main"
                                                                        width={680}
                                                                        align="center"
                                                                        border={0}
                                                                        cellPadding="0"
                                                                        cellSpacing="0"
                                                                        style={{
                                                                            width: '680px',
                                                                            margin: '0 auto',
                                                                            borderSpacing: 0
                                                                        }}
                                                                    >
                                                                        <tbody>
                                                                            <tr>
                                                                                <td
                                                                                    height={24}
                                                                                    style={{ lineHeight: '1px', fontSize: '1px' }}
                                                                                >
                                                                                    &nbsp;
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td
                                                                                    align="center"
                                                                                    style={{ textAlign: 'center', width: '50px' }}
                                                                                >
                                                                                    <div>
                                                                                        <a
                                                                                            href="https://aws.amazon.com/?sc_channel=em&sc_campaign=&sc_publisher=aws&sc_medium=em_&sc_content=&sc_country=&sc_region="
                                                                                            target="_blank"
                                                                                            rel="noopener noreferrer"
                                                                                        >
                                                                                            <img
                                                                                                src="https://pages.awscloud.com/rs/112-TZM-766/images/AWS_logo_RGB_BLK 1-dark.png"
                                                                                                width={50}
                                                                                                border={0}
                                                                                                alt="AWS Logo"
                                                                                            />
                                                                                        </a>
                                                                                    </div>
                                                                                </td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>

                                                                    {/* MS Outlook specific code closing */}
                                                                    {/* {`<!--[if gte mso 9]>
                                      </v:textbox>
                                    </v:rect>
                                  <![endif]-->`} */}
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    {/* Content Module After Wrap */}
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td
                                                height={40}
                                                style={{ lineHeight: '1px', fontSize: '1px' }}
                                            >
                                                &nbsp;
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    );
};
