// @ts-nocheck
import React from 'react';
import parse from "html-react-parser";

interface ImageCardItem {
    imageUrl: string;
    imageAlt: string;
    title: string;
    description: string;
    linkText: string;
    linkUrl: string;
}

interface TwoColumnImagesProps {
    cards: ImageCardItem[];
}

export const TwoColumnImages: React.FC<TwoColumnImagesProps> = ({
    cards = [
        {
            imageUrl: "https://pages.awscloud.com/rs/112-TZM-766/images/Image-Placeholder288x160.png?version=1",
            imageAlt: "Image Placeholder",
            title: "Title, sentence case, max 2 line",
            description: "Body: Provide a clear, detailed block of copy that expands upon the title. Include supporting facts.",
            // linkText: "Link CTA",
            // linkUrl: "https://"
        },
        {
            imageUrl: "https://pages.awscloud.com/rs/112-TZM-766/images/Image-Placeholder288x160.png?version=1",
            imageAlt: "Image Placeholder",
            title: "Title, sentence case, max 2 line",
            description: "Body: Provide a clear, detailed block of copy that expands upon the title. Include supporting facts.",
            // linkText: "Link CTA",
            // linkUrl: "https://"
        }
    ]
}) => {
    return (
        <div style={{ maxWidth: "600px", margin: "0 auto" }} className="mktoModule" id="module-03527ea3ae-ab07-46a1-b872-916b69342362">
            <td>
                <table width="100%" cellPadding={0} cellSpacing={0}
                    style={{ borderSpacing: 0, msoTableLspace: '0pt', msoTableRspace: '0pt', margin: '0 auto', width: '100%' }}
                    border={0} align="center">
                    <tbody>
                        <tr>
                            <td className="lightMode" style={{ backgroundColor: '#ffffff' }} bgcolor="#ffffff">
                                <table className="deviceWidth1" width={600} cellPadding={0} cellSpacing={0}
                                    style={{ borderSpacing: 0, msoTableLspace: '0pt', msoTableRspace: '0pt', margin: '0 auto', width: '600px' }}
                                    border={0} align="center">
                                    <tbody>
                                        <tr>
                                            <td>
                                                <table width="100%" cellPadding={0} cellSpacing={0}
                                                    style={{ borderSpacing: 0, msoTableLspace: '0pt', msoTableRspace: '0pt', margin: '0 auto', width: '100%' }}
                                                    border={0} align="center">
                                                    <tbody>
                                                        <tr>
                                                            {cards.map((card, index) => (
                                                                <React.Fragment key={index}>
                                                                    <th className="block" valign="top" width={288}
                                                                        style={{ fontFamily: 'AmazonEmber, Helvetica, arial, sans-serif', fontWeight: 400 }}>
                                                                        <table width="100%" cellPadding={0} cellSpacing={0}
                                                                            style={{ borderSpacing: 0, msoTableLspace: '0pt', msoTableRspace: '0pt', margin: '0 auto', width: '100%' }}
                                                                            border={0} align="center">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td className="full-width" style={{ textAlign: 'center' }}>
                                                                                        <div>
                                                                                            <img src={card.imageUrl}

                                                                                                width={288}
                                                                                                border={0}
                                                                                                style={{ width: '288px', border: 0, borderRadius: '8px', msoHide: 'all' }}
                                                                                            />
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td height={24} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td className="dark"
                                                                                        style={{
                                                                                            textAlign: 'left', fontFamily: 'AmazonEmber, Helvetica, arial, sans-serif',
                                                                                            fontSize: '20px', lineHeight: '24px', color: '#161D26',
                                                                                            msoLineHeightRule: 'exactly', fontWeight: 600
                                                                                        }}>
                                                                                        <div>{card.title}</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td height={10} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td className="dark"
                                                                                        style={{
                                                                                            textAlign: 'left', fontFamily: 'AmazonEmber, Helvetica, arial, sans-serif',
                                                                                            fontSize: '16px', lineHeight: '24px', color: '#232B37',
                                                                                            msoLineHeightRule: 'exactly', fontWeight: 400
                                                                                        }}>
                                                                                        <div>{parse(card.description)}</div>
                                                                                    </td>
                                                                                </tr>
                                                                                {/* <tr>
                                                                                    <td height={10} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td className="block" valign="middle">
                                                                                        <table align="left" width="100%" cellPadding={0} cellSpacing={0} border={0}>
                                                                                            <tbody>
                                                                                                <tr>
                                                                                                    <td>
                                                                                                        <div>
                                                                                                            <table align="left" border={0} cellSpacing={0} cellPadding={0}>
                                                                                                                <tbody>
                                                                                                                    <tr>
                                                                                                                        <td style={{
                                                                                                                            fontFamily: 'AmazonEmber, Helvetica, arial, sans-serif',
                                                                                                                            fontSize: '16px', lineHeight: '24px', textAlign: 'left',
                                                                                                                            fontWeight: 400, display: 'inline-block'
                                                                                                                        }}>
                                                                                                                            <div>
                                                                                                                                <a style={{ color: '#006BD6', outline: 'none', textDecoration: 'underline' }}
                                                                                                                                    href={card.linkUrl} target="_blank" rel="noopener noreferrer">{card.linkText}</a>
                                                                                                                            </div>
                                                                                                                        </td>
                                                                                                                    </tr>
                                                                                                                </tbody>
                                                                                                            </table>
                                                                                                        </div>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </tbody>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr> */}
                                                                            </tbody>
                                                                        </table>
                                                                    </th>
                                                                    {index < cards.length - 1 && (
                                                                        <th className="block" width={24} style={{ fontSize: '1px', lineHeight: '1px' }}>&nbsp;</th>
                                                                    )}
                                                                </React.Fragment>
                                                            ))}
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td height="40" style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </div>
    );
};
