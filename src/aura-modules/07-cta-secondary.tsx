// @ts-nocheck
import React from 'react';

interface CTASecondaryModuleProps {
    ctaText: string;
    ctaUrl: string;
}

export const CTASecondaryModule: React.FC<CTASecondaryModuleProps> = ({
    ctaText = "Secondary CTA",
    ctaUrl = "https://aws.amazon.com"
}) => {
    return (
        <div style={{ maxWidth: "600px", margin: "0 auto" }} className="mktoModule" id="module-03527ea3ae-ab07-46a1-b872-916b69342362">
            <td>
                <table
                    width="100%"
                    cellPadding="0"
                    cellSpacing="0"
                    style={{
                        borderSpacing: 0,
                        msoTableLspace: '0pt',
                        msoTableRspace: '0pt',
                        margin: '0 auto',
                        width: '100%'
                    }}
                    border={0}
                    align="center"
                >
                    <tbody>
                        <tr>
                            <td
                                className="lightMode"
                                style={{ backgroundColor: '#ffffff' }}
                                bgcolor="#ffffff"
                            >
                                <table
                                    className="deviceWidth1"
                                    width={600}
                                    cellPadding="0"
                                    cellSpacing="0"
                                    style={{
                                        borderSpacing: 0,
                                        msoTableLspace: '0pt',
                                        msoTableRspace: '0pt',
                                        margin: '0 auto',
                                        width: '600px'
                                    }}
                                    border={0}
                                    align="center"
                                >
                                    <tbody>
                                        <tr>
                                            <td height={0} style={{ lineHeight: '0px', fontSize: '0px' }}></td>
                                        </tr>
                                        <tr>
                                            <td
                                                className="dark"
                                                valign="top"
                                                style={{
                                                    fontFamily: 'AmazonEmber,arial, Helvetica, sans-serif',
                                                    fontSize: '16px',
                                                    lineHeight: '24px',
                                                    fontWeight: 'normal',
                                                    color: '#232B37',
                                                    textAlign: 'left',
                                                    msoLineHeightRule: 'exactly'
                                                }}
                                            >
                                                <div>
                                                    {/* Content Module Before Wrap */}
                                                    <table align="left" cellPadding="0" cellSpacing="0" border={0}>
                                                        <tbody>
                                                            <tr>
                                                                <td
                                                                    className="btn-darkMode2"
                                                                    style={{
                                                                        borderRadius: '40px',
                                                                        backgroundColor: '#ffffff'
                                                                    }}
                                                                    align="center"
                                                                >
                                                                    <a
                                                                        href={ctaUrl}
                                                                        target="_blank"
                                                                        style={{
                                                                            fontSize: '16px',
                                                                            fontFamily: 'AmazonEmber, Helvetica, arial,sans-serif',
                                                                            fontWeight: 'bold',
                                                                            color: '#151d26',
                                                                            textDecoration: 'none',
                                                                            display: 'inline-block',
                                                                            border: '2px solid #161D26',
                                                                            padding: '12px 24px',
                                                                            borderRadius: '50px',
                                                                            lineHeight: '20px',
                                                                            textAlign: 'center'
                                                                        }}
                                                                    >
                                                                        {ctaText}
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    {/* Content Module After Wrap */}
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td height={40} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </div>
    );
};

export default CTASecondaryModule;
