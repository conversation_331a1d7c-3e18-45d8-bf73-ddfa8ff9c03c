// @ts-nocheck
import React from 'react';

interface EventDetailsOnlineProps {
    eventType: string;
    date: string;
    startTime: string;
    endTime: string;
    timezone: string;
    virtualLink: string;
    virtualLinkText: string;
}

export const EventDetailsOnline: React.FC<EventDetailsOnlineProps> = ({
    eventType = "Online Event",
    date = "November 06, 2024",
    startTime = "1:00PM",
    endTime = "2:00PM",
    timezone = "EST",
    virtualLink = "https://",
    virtualLinkText = "Link"
}) => {
    const formatDate = (dateString: string) => {
        const [day, month, year] = dateString.split('/');
        const date = new Date(`${year}-${month}-${day}`);

        return date.toLocaleDateString('en-US', {
            month: 'long',
            day: 'numeric',
            year: 'numeric'
        });
    };

    const formatTime = (time: string) => {
        const [hours, minutes] = time.split(':');
        const parsedHours = parseInt(hours);
        const period = parsedHours >= 12 ? 'PM' : 'AM';
        const displayHours = parsedHours % 12 || 12;
        return `${displayHours}:${minutes} ${period}`;
    };

    const formattedDate = formatDate(date);
    const formattedTimeRange = `${formatTime(startTime)} – ${formatTime(endTime)}`;

    return (
        <div style={{ maxWidth: "600px", margin: "0 auto" }} className="mktoModule" id="module-03527ea3ae-ab07-46a1-b872-916b69342362">
            <table style={{ borderSpacing: 0, msoTableLspace: '0pt', msoTableRspace: '0pt', borderCollapse: 'collapse' }}
                align="center" border={0} cellPadding={0} cellSpacing={0} width="100%">
                <tbody>
                    <tr>
                        <td className="lightMode" style={{ backgroundColor: '#ffffff' }} bgcolor="#ffffff">
                            <table className="deviceWidth1" width={600} cellPadding={0} cellSpacing={0}
                                style={{
                                    borderSpacing: 0, msoTableLspace: '0pt', msoTableRspace: '0pt', margin: '0 auto',
                                    width: '600px', border: '1px solid #D1D1D6', borderRadius: '12px'
                                }}
                                border={0} align="center">
                                <tbody>
                                    <tr>
                                        <td>
                                            <table className="deviceWidth1" width={568} cellPadding={0} cellSpacing={0}
                                                style={{
                                                    borderSpacing: 0, msoTableLspace: '0pt', msoTableRspace: '0pt',
                                                    margin: '0 auto', width: '568px'
                                                }}
                                                border={0} align="center">
                                                <tbody>
                                                    <tr>
                                                        <td colSpan={2} height={16} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                                    </tr>
                                                    <tr>
                                                        <th width={128} valign="top" style={{ verticalAlign: 'top', width: '128px' }}>
                                                            <table cellPadding={0} cellSpacing={0} align="center" width="100%" border={0}>
                                                                <tbody>
                                                                    <tr>
                                                                        <td style={{
                                                                            paddingRight: '8px', textAlign: 'left', color: '#161D26',
                                                                            fontSize: '26px', fontWeight: 500, lineHeight: '30px'
                                                                        }}>
                                                                            <div style={{
                                                                                textAlign: 'left', fontFamily: 'AmazonEmber, Helvetica, arial, sans-serif',
                                                                                fontSize: '20px', lineHeight: '24px', color: '#161d26',
                                                                                msoLineHeightRule: 'exactly', fontWeight: 600
                                                                            }}>
                                                                                {eventType}
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </th>
                                                        <th width={20} style={{ fontSize: '1px', lineHeight: '1px', borderLeft: '1px solid #B6BEC9' }}>&nbsp;</th>
                                                        <th width={420} className="pd-lft" valign="top" style={{ verticalAlign: 'top', width: '420px' }}>
                                                            <table cellPadding={0} cellSpacing={0} align="center" width="100%" border={0}>
                                                                <tbody>
                                                                    <tr>
                                                                        <td className="block" width={196}
                                                                            style={{
                                                                                textAlign: 'left', fontFamily: 'AmazonEmber, Helvetica, arial, sans-serif',
                                                                                color: '#232B37', msoLineHeightRule: 'exactly', width: '196px'
                                                                            }}>
                                                                            <table cellPadding={0} cellSpacing={0} align="center" width="100%" border={0}>
                                                                                <tbody>
                                                                                    <tr>
                                                                                        <td>
                                                                                            <div style={{ fontSize: '20px', lineHeight: '24px', fontWeight: 600 }}>
                                                                                                Date
                                                                                            </div>
                                                                                        </td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td height={8} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td>
                                                                                            <div style={{ fontSize: '16px', lineHeight: '24px', fontWeight: 400 }}>

                                                                                                {formattedDate}<br />
                                                                                                {formattedTimeRange} {timezone}

                                                                                            </div>
                                                                                        </td>
                                                                                    </tr>
                                                                                </tbody>
                                                                            </table>
                                                                        </td>
                                                                        <td className="block pad-top10 border-btm" width={22}
                                                                            style={{ borderRight: '1px solid #B6BEC9', fontSize: '1px', lineHeight: '1px' }}></td>
                                                                        <td className="block" width={22} style={{ fontSize: '1px', lineHeight: '1px' }}></td>
                                                                        <td className="block pad-top10" width={182} valign="top"
                                                                            style={{
                                                                                textAlign: 'left', fontFamily: 'AmazonEmber, Helvetica, arial, sans-serif',
                                                                                color: '#232B37', msoLineHeightRule: 'exactly', fontWeight: 400, width: '182px'
                                                                            }}>
                                                                            <table cellPadding={0} cellSpacing={0} align="center" width="100%" border={0}>
                                                                                <tbody>
                                                                                    <tr>
                                                                                        <td>
                                                                                            <div style={{ fontSize: '20px', lineHeight: '24px', fontWeight: 600 }}>
                                                                                                Virtual link
                                                                                            </div>
                                                                                        </td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td height={8} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td>
                                                                                            <div>
                                                                                                <a style={{ color: '#006BD6', outline: 'none', textDecoration: 'underline' }}
                                                                                                    href={virtualLink}>{virtualLinkText}</a>
                                                                                            </div>
                                                                                        </td>
                                                                                    </tr>
                                                                                </tbody>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </th>
                                                    </tr>
                                                    <tr>
                                                        <td colSpan={2} height={16} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td height="40" style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                    </tr>
                </tbody>
            </table>
        </div>
    );
};

export default EventDetailsOnline;
