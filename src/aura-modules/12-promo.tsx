// @ts-nocheck
import React from 'react';
import parse from "html-react-parser";

interface PromoBlockProps {
    title: string;
    description: string;
    linkText: string;
    linkUrl: string;
}

export const PromoBlock: React.FC<PromoBlockProps> = ({
    title = "AWS Executive highlights from re:Invent 2024",
    description = "Hear insights from AWS and industry thought leaders on key topics to inspire innovation and transformation including fireside chats with <PERSON> and <PERSON>",
    linkText = "Register now",
    linkUrl = "https://emea-reinvent-recap.virtual.awsevents.com/register"
}) => {
    return (
        <div style={{ maxWidth: "600px", margin: "0 auto" }} className="mktoModule" id="module-03527ea3ae-ab07-46a1-b872-916b69342362">
            <td>
                <table width="100%" cellPadding={0} cellSpacing={0}
                    style={{ borderSpacing: 0, msoTableLspace: '0pt', msoTableRspace: '0pt', margin: '0 auto', width: '100%' }}
                    border={0} align="center">
                    <tbody>
                        <tr>
                            <td className="lightMode" style={{ backgroundColor: '#ffffff' }} bgcolor="#ffffff">
                                <table className="deviceWidth1" width={600} cellPadding={0} cellSpacing={0}
                                    style={{ borderSpacing: 0, msoTableLspace: '0pt', msoTableRspace: '0pt', margin: '0 auto', width: '600px' }}
                                    border={0} align="center">
                                    <tbody>
                                        <tr>
                                            <td height={0} style={{ lineHeight: '0px', fontSize: '0px' }}></td>
                                        </tr>
                                        <tr>
                                            <td className="dark" valign="top"
                                                style={{
                                                    fontFamily: 'AmazonEmber,arial, Helvetica, sans-serif', fontSize: '16px',
                                                    lineHeight: '24px', fontWeight: 'normal', color: '#232B37',
                                                    textAlign: 'left', msoLineHeightRule: 'exactly'
                                                }}>
                                                <div>
                                                    <table className="pointerCursor"
                                                        style={{ borderSpacing: 0, borderCollapse: 'collapse' }}
                                                        align="center" border={0} cellPadding={0} cellSpacing={0} width="100%">
                                                        <tbody>
                                                            <tr>
                                                                <td bgcolor="#F3F3F6" border={0}
                                                                    style={{
                                                                        backgroundRepeat: 'no-repeat', backgroundPosition: 'center',
                                                                        backgroundSize: 'cover', backgroundColor: '#f3f3f6',
                                                                        borderTopLeftRadius: '8px', borderTopRightRadius: '8px',
                                                                        borderBottomRightRadius: '8px', borderBottomLeftRadius: '8px'
                                                                    }}>
                                                                    <div style={{ fontSize: '0px', lineHeight: '0px' }}>
                                                                        <table width={540} cellPadding={0} cellSpacing={0}
                                                                            style={{ borderSpacing: 0, margin: '0 auto' }}
                                                                            border={0} align="center" className="deviceWidth1">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td valign="top"
                                                                                        style={{
                                                                                            verticalAlign: 'top', color: '#232f3e',
                                                                                            fontSize: '16px', lineHeight: '24px',
                                                                                            textAlign: 'left',
                                                                                            fontFamily: 'AmazonEmber,arial, Helvetica, sans-serif',
                                                                                            fontWeight: 'normal'
                                                                                        }}>
                                                                                        <div>
                                                                                            <p>&nbsp;<br /></p>
                                                                                            <p style={{
                                                                                                verticalAlign: 'top',
                                                                                                fontFamily: 'AmazonEmber,arial,helvetica',
                                                                                                fontWeight: 'bold',
                                                                                                textAlign: 'left',
                                                                                                fontSize: '20px'
                                                                                            }}>
                                                                                                {title}<br />
                                                                                            </p>
                                                                                            <p>{parse(description)}</p>
                                                                                            <p>
                                                                                                <a href={linkUrl}
                                                                                                    style={{ color: '#006bd6', textDecoration: 'underline' }}
                                                                                                    target="_blank" rel="noopener noreferrer">
                                                                                                    {linkText}
                                                                                                </a>
                                                                                            </p>
                                                                                            <p>&nbsp;<br /></p>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        <br />
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td height={40} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </div>
    );
};

export default PromoBlock;
