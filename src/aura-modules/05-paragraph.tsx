// @ts-nocheck
// ParagraphModule.tsx
import React from 'react';
import parse from "html-react-parser";

interface ParagraphModuleProps {
    moduleId?: string;
}

export const ParagraphModule: React.FC<ParagraphModuleProps> = ({
    content
}) => {
    return (
        <div style={{ maxWidth: "600px", margin: "0 auto" }} className="mktoModule" id="module-03527ea3ae-ab07-46a1-b872-916b69342362">
            <td>
                <table
                    width="100%"
                    cellPadding="0"
                    cellSpacing="0"
                    style={{
                        borderSpacing: 0,
                        msoTableLspace: '0pt',
                        msoTableRspace: '0pt',
                        margin: '0 auto',
                        width: '100%'
                    }}
                    border={0}
                    align="center"
                >
                    <tbody>
                        <tr>
                            <td
                                className="lightMode"
                                style={{ backgroundColor: '#ffffff' }}
                                bgcolor="#ffffff"
                            >
                                <table
                                    className="deviceWidth1"
                                    width={600}
                                    cellPadding="0"
                                    cellSpacing="0"
                                    style={{
                                        borderSpacing: 0,
                                        msoTableLspace: '0pt',
                                        msoTableRspace: '0pt',
                                        margin: '0 auto',
                                        width: '600px'
                                    }}
                                    border={0}
                                    align="center"
                                >
                                    <tbody>
                                        <tr>
                                            <td height={0} style={{ lineHeight: '0px', fontSize: '0px' }}></td>
                                        </tr>
                                        <tr>
                                            <td
                                                className="dark"
                                                valign="top"
                                                style={{

                                                    lineHeight: '24px',

                                                }}
                                            >
                                                <div>
                                                    {/* Content Module Before Wrap */}
                                                    {parse(content)}
                                                    {/* Content Module After Wrap */}
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td height={40} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </div>
    );
};

export default ParagraphModule;
