// @ts-nocheck
// SubHeadingModule.tsx
import React from 'react';

interface SubHeadingModuleProps {
    headingText: string;
}

export const SubHeadingModule: React.FC<SubHeadingModuleProps> = ({
    headingText = "Title, sentence case, max 2 lines"
}) => {
    return (
        <div style={{ maxWidth: "600px", margin: "0 auto" }} className="mktoModule" id="module-03-37400b9c6-30f1-4092-ac94-4be38281766b">
            <td>
                <table
                    width="100%"
                    cellPadding="0"
                    cellSpacing="0"
                    style={{
                        borderSpacing: 0,
                        msoTableLspace: '0pt',
                        msoTableRspace: '0pt',
                        margin: '0 auto',
                        width: '100%'
                    }}
                    border={0}
                    align="center"
                >
                    <tbody>
                        <tr>
                            <td
                                className="lightMode"
                                style={{ backgroundColor: '#ffffff' }}
                                bgcolor="#ffffff"
                            >
                                <table
                                    className="deviceWidth1"
                                    width={600}
                                    cellPadding="0"
                                    cellSpacing="0"
                                    style={{
                                        borderSpacing: 0,
                                        msoTableLspace: '0pt',
                                        msoTableRspace: '0pt',
                                        margin: '0 auto',
                                        width: '600px'
                                    }}
                                    border={0}
                                    align="center"
                                >
                                    <tbody>
                                        <tr>
                                            <td height={0} style={{ lineHeight: '0px', fontSize: '0px' }}></td>
                                        </tr>
                                        <tr>
                                            <td
                                                className="dark font-24"
                                                valign="top"
                                                style={{
                                                    fontFamily: "'AmazonEmber', Helvetica, arial, sans-serif",
                                                    fontSize: '28px',
                                                    lineHeight: '36px',
                                                    fontWeight: 'bold',
                                                    color: '#161D26',
                                                    textAlign: 'left',
                                                    msoLineHeightRule: 'exactly'
                                                }}
                                            >
                                                <div>
                                                    {/* Title Module Before Wrap */}
                                                    <div style={{ marginTop: '10px', marginBottom: '10px' }}>
                                                        {headingText}
                                                    </div>
                                                    {/* Title Module After Wrap */}
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td height={16} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>

                    </tbody>
                </table>
            </td>
        </div>
    );
};

export default SubHeadingModule;
