// @ts-nocheck
import React from 'react';

interface AgendaItem {
    time: string;
    sessionName: string;
}

interface EventAgendaProps {
    timezone?: string;
    agendaItems: AgendaItem[];
}

export const EventAgenda: React.FC<EventAgendaProps> = ({
    title,
    timezone,
    sessions,
    agendaItems
}) => {

    const formatTime = (time: string) => {
        const [hours, minutes] = time.split(':');
        const parsedHours = parseInt(hours);
        const period = parsedHours >= 12 ? 'PM' : 'AM';
        const displayHours = parsedHours % 12 || 12;
        return `${displayHours}:${minutes} ${period}`;
    };




    return (
        <div style={{ maxWidth: "600px", margin: "0 auto" }} className="mktoModule" id="module-03527ea3ae-ab07-46a1-b872-916b69342362">
            <td>

                <table width="100%" cellPadding={0} cellSpacing={0} style={{ borderSpacing: 0, msoTableLspace: '0pt', msoTableRspace: '0pt', margin: '0 auto', width: '100%' }} border={0} align="center">
                    <tbody>
                        <tr>
                            <td className="lightMode" style={{ backgroundColor: '#ffffff' }} bgcolor="#ffffff">
                                <table className="deviceWidth1" width={600} cellPadding={0} cellSpacing={0} style={{ borderSpacing: 0, msoTableLspace: '0pt', msoTableRspace: '0pt', margin: '0 auto', width: 600 }} border={0} align="center">
                                    <tbody>
                                        <tr>
                                            <td height={0} style={{ lineHeight: 0, fontSize: 0 }} />
                                        </tr>
                                        <tr>
                                            <td className="dark" valign="top" style={{ fontFamily: 'AmazonEmber,arial, Helvetica, sans-serif', fontSize: '16px', lineHeight: '24px', fontWeight: 'normal', color: '#232B37', textAlign: 'left', msoLineHeightRule: 'exactly' }}>
                                                <div>
                                                    {/* Content Module Before Wrap */}
                                                    <table cellPadding={0} cellSpacing={0} align="center" border={0} width="100%" style={{ margin: '0 auto', minWidth: '100%' }}>
                                                        <tbody>
                                                            <tr>
                                                                <td className="darkMode lightMode" bgcolor="#ffffff" style={{ backgroundColor: '#ffffff' }}>
                                                                    <table className="deviceWidth1" width={600} cellPadding={0} cellSpacing={0} style={{ borderSpacing: 0, msoTableLspace: '0pt', msoTableRspace: '0pt', margin: '0 auto', width: 600 }} border={0} align="center">
                                                                        <tbody>
                                                                            <tr>
                                                                                <td height={0} style={{ fontSize: 0, lineHeight: 0 }} />
                                                                            </tr>
                                                                            <tr>
                                                                                <td className="dark" valign="top" style={{ verticalAlign: 'top', color: '#161D26', fontSize: '28px', lineHeight: '36px', textAlign: 'left', fontFamily: '"AmazonEmber", Helvetica, arial, sans-serif', fontWeight: 600 }}>
                                                                                    <div>{title}</div>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td height={16} style={{ fontSize: 1, lineHeight: 1 }}>&nbsp;</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td className="dark" style={{ verticalAlign: 'top', color: '#232F3E', fontSize: '14px', lineHeight: '20px', textAlign: 'left', fontFamily: '"AmazonEmber", Helvetica, arial, sans-serif', fontWeight: 'normal' }}>
                                                                                    <div>
                                                                                        <table width="100%" cellPadding={0} cellSpacing={0} border={0} style={{ border: 0 }}>
                                                                                            <tbody>
                                                                                                <tr>
                                                                                                    <td bgcolor="#F3F3F6" valign="top" className="agendaWidth" width={300} style={{ fontSize: '16px', lineHeight: '24px', padding: '16px 16px 16px 16px', border: '1px solid #D1D1D6', color: '#161D26', fontWeight: 600, width: 150, borderTopLeftRadius: 8 }}>{timezone}</td>
                                                                                                    <td width={300} bgcolor="#F3F3F6" valign="top" className="agendaPad" style={{ fontSize: '16px', lineHeight: '24px', padding: '16px 16px 16px 16px', borderTop: '1px solid #D1D1D6', borderBottom: '1px solid #D1D1D6', borderRight: '1px solid #D1D1D6', color: '#161D26', fontWeight: 600, width: 450, borderTopRightRadius: 8 }}>{sessions}</td>
                                                                                                </tr>

                                                                                                {agendaItems.map((item, index) => (
                                                                                                    <tr key={index}>
                                                                                                        <td valign="top" className="agendaPad"
                                                                                                            style={{
                                                                                                                fontSize: '14px', lineHeight: '20px',
                                                                                                                color: '#141F2E', padding: '16px',
                                                                                                                borderBottom: '1px solid #D1D1D6',
                                                                                                                borderRight: '1px solid #D1D1D6',
                                                                                                                borderLeft: '1px solid #D1D1D6'
                                                                                                            }}>
                                                                                                            {item.time}
                                                                                                        </td>
                                                                                                        <td valign="top" className="agendaPad"
                                                                                                            style={{
                                                                                                                fontSize: '14px', lineHeight: '20px',
                                                                                                                color: '#141F2E', padding: '16px',
                                                                                                                borderBottom: '1px solid #D1D1D6',
                                                                                                                borderRight: '1px solid #D1D1D6'
                                                                                                            }}>
                                                                                                            {item.sessionName}
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                ))}
                                                                                            </tbody>
                                                                                        </table>
                                                                                    </div>
                                                                                </td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table> </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    {/* Content Module After Wrap */}
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td height={40} style={{ lineHeight: 1, fontSize: 1 }}>&nbsp;</td>
                                        </tr>
                                    </tbody>
                                </table> </td>
                        </tr>
                    </tbody>
                </table>

            </td>
        </div>
    );
};

export default EventAgenda;
