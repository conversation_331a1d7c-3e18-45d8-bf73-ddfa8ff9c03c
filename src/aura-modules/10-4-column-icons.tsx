// @ts-nocheck
import React from 'react';

interface IconColumn {
    iconSrc: string;
    title: string;
}

interface FourColumnIconsModuleProps {
    columns: IconColumn[];
}

export const FourColumnIconsModule: React.FC<FourColumnIconsModuleProps> = ({
    columns = [
        {
            iconSrc: "https://pages.awscloud.com/rs/112-TZM-766/images/aws-email-icon-Cloud-t4.png",
            title: "Live hands-on demos"
        },
        {
            iconSrc: "https://pages.awscloud.com/rs/112-TZM-766/images/aws-email-icon-Chat-t4.png",
            title: "Announcements from the re:Invent Keynotes"
        },
        {
            iconSrc: "https://pages.awscloud.com/rs/112-TZM-766/images/aws-email-icon-Sessions-t4.png",
            title: "Get your technical questions answered"
        },
        {
            iconSrc: "https://pages.awscloud.com/rs/112-TZM-766/images/aws-email-icon-Innovation-t4.png",
            title: "Learn what's new on the platform"
        }
    ]
}) => {
    return (
        <div style={{ maxWidth: "600px", margin: "0 auto" }} className="mktoModule" id="module-03527ea3ae-ab07-46a1-b872-916b69342362">
            <td>
                <table
                    width="100%"
                    cellPadding="0"
                    cellSpacing="0"
                    style={{
                        borderSpacing: 0,
                        msoTableLspace: '0pt',
                        msoTableRspace: '0pt',
                        margin: '0 auto',
                        width: '100%'
                    }}
                    border={0}
                    align="center"
                >
                    <tbody>
                        <tr>
                            <td
                                className="lightMode"
                                style={{ backgroundColor: '#ffffff' }}
                                bgcolor="#ffffff"
                            >
                                <table
                                    className="deviceWidth1"
                                    width={600}
                                    cellPadding="0"
                                    cellSpacing="0"
                                    style={{
                                        borderSpacing: 0,
                                        msoTableLspace: '0pt',
                                        msoTableRspace: '0pt',
                                        margin: '0 auto',
                                        width: '600px'
                                    }}
                                    border={0}
                                    align="center"
                                >
                                    <tbody>
                                        <tr>
                                            <td height={0} style={{ lineHeight: '0px', fontSize: '0px' }}></td>
                                        </tr>
                                        <tr>
                                            <td
                                                className="dark"
                                                valign="top"
                                                style={{
                                                    fontFamily: 'AmazonEmber,arial, Helvetica, sans-serif',
                                                    fontSize: '16px',
                                                    lineHeight: '24px',
                                                    fontWeight: 'normal',
                                                    color: '#232B37',
                                                    textAlign: 'left',
                                                    msoLineHeightRule: 'exactly'
                                                }}
                                            >
                                                <div>
                                                    {/* Content Module Before Wrap */}
                                                    <table align="center" width="100%" cellPadding="0" cellSpacing="0" border={0}>
                                                        <tbody>
                                                            <tr>
                                                                <th className="block deviceWidth" width="300" valign="top" style={{ textAlign: 'left', fontWeight: 400 }}>
                                                                    <table align="center" width="100%" cellPadding="0" cellSpacing="0" border={0}>
                                                                        <tbody>
                                                                            <tr>
                                                                                {columns.slice(0, 2).map((column, index) => (
                                                                                    <React.Fragment key={index}>
                                                                                        <th width="135" valign="top" style={{ textAlign: 'left', fontWeight: 400 }}>
                                                                                            <table align="center" width="100%" cellPadding="0" cellSpacing="0" border={0}>
                                                                                                <tbody>
                                                                                                    <tr>
                                                                                                        <td align="center">
                                                                                                            <img
                                                                                                                src={column.iconSrc}
                                                                                                                width={100}
                                                                                                                style={{ maxWidth: '100px' }}
                                                                                                                height="auto"
                                                                                                                alt=""
                                                                                                            />
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    <tr>
                                                                                                        <td height="13" style={{ fontSize: '1px', lineHeight: '1px' }}>&nbsp;</td>
                                                                                                    </tr>
                                                                                                    <tr>
                                                                                                        <td
                                                                                                            valign="top"
                                                                                                            style={{
                                                                                                                verticalAlign: 'top',
                                                                                                                fontSize: '14px',
                                                                                                                lineHeight: '20px',
                                                                                                                fontFamily: "'AmazonEmber',arial,helvetica",
                                                                                                                fontWeight: 'bold',
                                                                                                                textAlign: 'center'
                                                                                                            }}
                                                                                                        >
                                                                                                            {column.title}
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    <tr>
                                                                                                        <td height="12" style={{ fontSize: '1px', lineHeight: '1px' }}>&nbsp;</td>
                                                                                                    </tr>
                                                                                                </tbody>
                                                                                            </table>
                                                                                        </th>
                                                                                        {index < 1 && (
                                                                                            <th width="20" height="20" style={{ fontSize: '1px', lineHeight: '1px' }}>&nbsp;</th>
                                                                                        )}
                                                                                    </React.Fragment>
                                                                                ))}
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </th>
                                                                <th className="block" width="20" height="20" style={{ fontSize: '1px', lineHeight: '1px' }}>&nbsp;</th>
                                                                <th className="block deviceWidth" valign="top" style={{ textAlign: 'left', fontWeight: 400 }}>
                                                                    <table align="center" width="100%" cellPadding="0" cellSpacing="0" border={0}>
                                                                        <tbody>
                                                                            <tr>
                                                                                {columns.slice(2, 4).map((column, index) => (
                                                                                    <React.Fragment key={index}>
                                                                                        <th width="135" valign="top" style={{ textAlign: 'left', fontWeight: 400 }}>
                                                                                            <table align="center" width="100%" cellPadding="0" cellSpacing="0" border={0}>
                                                                                                <tbody>
                                                                                                    <tr>
                                                                                                        <td className="full-width" align="center">
                                                                                                            <img
                                                                                                                src={column.iconSrc}
                                                                                                                width={100}
                                                                                                                style={{ maxWidth: '100px' }}
                                                                                                                height="auto"
                                                                                                                alt=""
                                                                                                            />
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    <tr>
                                                                                                        <td height="13" style={{ fontSize: '1px', lineHeight: '1px' }}>&nbsp;</td>
                                                                                                    </tr>
                                                                                                    <tr>
                                                                                                        <td
                                                                                                            valign="top"
                                                                                                            style={{
                                                                                                                verticalAlign: 'top',
                                                                                                                fontSize: '14px',
                                                                                                                lineHeight: '20px',
                                                                                                                fontFamily: "'AmazonEmber',arial,helvetica",
                                                                                                                fontWeight: 'bold',
                                                                                                                textAlign: 'center'
                                                                                                            }}
                                                                                                        >
                                                                                                            {column.title}
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    <tr>
                                                                                                        <td height="12" style={{ fontSize: '1px', lineHeight: '1px' }}>&nbsp;</td>
                                                                                                    </tr>
                                                                                                </tbody>
                                                                                            </table>
                                                                                        </th>
                                                                                        {index < 1 && (
                                                                                            <th width="20" height="20" style={{ fontSize: '1px', lineHeight: '1px' }}>&nbsp;</th>
                                                                                        )}
                                                                                    </React.Fragment>
                                                                                ))}
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </th>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    {/* Content Module After Wrap */}
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td height={40} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </div>
    );
};

export default FourColumnIconsModule;
