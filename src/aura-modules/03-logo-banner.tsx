// @ts-nocheck
// LogoBannerModule.tsx
import React from 'react';

interface LogoBannerModuleProps {
    moduleId?: string;
    bannerSrc?: string;
}

export const LogoBannerModule: React.FC<LogoBannerModuleProps> = ({
    moduleId = "module-03-37400b9c6-30f1-4092-ac94-4be38281766b",
    bannerSrc = "https://pages.awscloud.com/rs/112-TZM-766/images/2025_AWS-reInvent-reCap-email-hero-banner-1200x600.png",
    ShowLogo = "yes"
}) => {
    return (
        <tr className="mktoModule" id={moduleId}>
            <td>
                <table
                    width="100%"
                    cellPadding="0"
                    cellSpacing="0"
                    style={{
                        borderSpacing: 0,
                        msoTableLspace: '0pt',
                        msoTableRspace: '0pt',
                        margin: '0 auto',
                        width: '100%'
                    }}
                    border={0}
                    align="center"
                >
                    <tbody>
                        <tr>
                            <td
                                className="lightMode"
                                style={{ backgroundColor: '#ffffff' }}
                                bgcolor="#ffffff"
                            >
                                <table
                                    className="deviceWidth1"
                                    width={600}
                                    cellPadding="0"
                                    cellSpacing="0"
                                    style={{
                                        borderSpacing: 0,
                                        msoTableLspace: '0pt',
                                        msoTableRspace: '0pt',
                                        margin: '0 auto',
                                        width: '600px'
                                    }}
                                    border={0}
                                    align="center"
                                >
                                    <tbody>
                                        <tr>
                                            <td height={0} style={{ lineHeight: '0px', fontSize: '0px' }}></td>
                                        </tr>
                                        <tr>
                                            <td
                                                className="dark font-24"
                                                valign="top"
                                                style={{
                                                    fontFamily: "'AmazonEmber', Helvetica, arial, sans-serif",
                                                    fontSize: '28px',
                                                    lineHeight: '36px',
                                                    fontWeight: 'bold',
                                                    color: '#161D26',
                                                    textAlign: 'left',
                                                    msoLineHeightRule: 'exactly'
                                                }}
                                            >
                                                <div>
                                                    <table
                                                        className="main"
                                                        width={600}
                                                        cellPadding="0"
                                                        cellSpacing="0"
                                                        style={{
                                                            borderSpacing: 0,
                                                            msoTableLspace: '0pt',
                                                            msoTableRspace: '0pt',
                                                            margin: '0 auto',
                                                            width: '600px'
                                                        }}
                                                        border={0}
                                                        align="center"
                                                    >
                                                        <tbody>
                                                            <tr >
                                                                <td valign="top" className="lightMode">
                                                                    <table
                                                                        className="main"
                                                                        width={680}
                                                                        align="center"
                                                                        border={0}
                                                                        cellPadding="0"
                                                                        cellSpacing="0"
                                                                        style={{
                                                                            width: '680px',
                                                                            margin: '0 auto',
                                                                            borderSpacing: 0
                                                                        }}
                                                                    >
                                                                        <tbody>
                                                                            <tr>
                                                                                <td height={24} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td align="center" style={{ textAlign: 'center', width: '50px' }}>
                                                                                    <div style={{ display: ShowLogo === "yes" ? "block" : "none" }}>
                                                                                        <a href="https://aws.amazon.com/" target="_blank">
                                                                                            <img
                                                                                                src="https://pages.awscloud.com/rs/112-TZM-766/images/AWS-Logo-new-rd.png"
                                                                                                width={75}
                                                                                                border={0}
                                                                                                alt="AWS Logo"
                                                                                            />
                                                                                        </a>
                                                                                    </div>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td height={24} style={{ lineHeight: '1px', fontSize: '1px', display: ShowLogo === "yes" ? "block" : "none" }}>&nbsp;</td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td
                                                                    className="lightMode"
                                                                    bgcolor="#ffffff"
                                                                    style={{ backgroundColor: '#ffffff' }}
                                                                >
                                                                    <table
                                                                        width="100%"
                                                                        cellPadding="0"
                                                                        cellSpacing="0"
                                                                        style={{
                                                                            borderSpacing: 0,
                                                                            msoTableLspace: '0pt',
                                                                            msoTableRspace: '0pt',
                                                                            margin: '0 auto',
                                                                            width: '100%',
                                                                            borderCollapse: 'collapse'
                                                                        }}
                                                                        border={0}
                                                                        className="deviceWidth1"
                                                                        align="center"
                                                                    >
                                                                        <tbody>
                                                                            <tr>
                                                                                <td
                                                                                    className="full-width"
                                                                                    valign="top"
                                                                                    style={{ textAlign: 'center' }}
                                                                                >
                                                                                    <div>
                                                                                        <img
                                                                                            src={bannerSrc}
                                                                                            width={600}
                                                                                            className="rounded-corners"
                                                                                            alt="Image placeholder"
                                                                                            border={0}
                                                                                            style={{
                                                                                                width: '600px',
                                                                                                border: 0,
                                                                                                borderRadius: '8px',
                                                                                                msoHide: 'all'
                                                                                            }}
                                                                                        />
                                                                                    </div>
                                                                                </td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td className="lightMode" height={24} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td height={16} style={{ lineHeight: '1px', fontSize: '1px' }}>&nbsp;</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    );
};

export default LogoBannerModule;
