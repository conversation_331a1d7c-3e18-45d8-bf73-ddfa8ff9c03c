import { API_CONFIG } from '../config/constants';
import { EmailData, APIResponse } from '../interfaces/email';

interface APIError {
  message: string;
  status?: number;
}

const handleError = (error: unknown): APIError => {
  if (error instanceof Error) {
    return { message: error.message };
  }
  if (typeof error === 'string') {
    return { message: error };
  }
  return { message: 'An unexpected error occurred' };
};

const callAPI = async (url: string, method: string, body?: object): Promise<APIResponse> => {
  try {
    const options: RequestInit = {
      method,
      headers: {
        ...API_CONFIG.HEADERS
      },
    };

    if (body) {
      options.body = JSON.stringify(body);
    }

    const response = await fetch(url, options);
    const result = await response.json();
    console.log('API Response:', response, url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // If result already has data property, use it as is
    // Otherwise, move body to data for consistency
    if (result.body && !result.data) {
      result.data = result.body;
    }

    return result.data;
  } catch (error) {
    console.error('catched in api.ts');
    const apiError = handleError(error);
    throw apiError;
  }
};

export const createEmail = async (emailData: EmailData): Promise<APIResponse> => {
  return await callAPI(`${API_CONFIG.BASE_URL}/emails`, 'POST', emailData);
};

export const getEmail = async (emailId: string): Promise<APIResponse> => {
  return await callAPI(`${API_CONFIG.BASE_URL}/emails/${emailId}`, 'GET');
};

export const updateEmail = async (emailId: string, emailData: EmailData): Promise<APIResponse> => {
  return await callAPI(`${API_CONFIG.BASE_URL}/emails/${emailId}`, 'PUT', emailData);
};

export const deleteEmail = async (emailId: string): Promise<APIResponse> => {
  return await callAPI(`${API_CONFIG.BASE_URL}/emails/${emailId}`, 'DELETE');
};

export const listEmails = async (emailOwner: string): Promise<APIResponse> => {
  console.log('listEmails called with emailOwner:', emailOwner);
  if (!emailOwner) {
    console.error('listEmails: emailOwner is required but was not provided');
    return {
      statusCode: 400,
      error: { message: "Email owner is required to list emails." }
    };
  }
  const url = `${API_CONFIG.BASE_URL}/emails?alias=${encodeURIComponent(emailOwner)}`;
  console.log('listEmails: calling API with URL:', url);
  try {
    const result = await callAPI(url, 'GET');
    console.log('listEmails: received response:', result);
    console.log('listEmails: response data type:', typeof result.data);
    console.log('listEmails: response data is array?', Array.isArray(result.data));
    if (result.data) {
      console.log('listEmails: response data length:', Array.isArray(result.data) ? result.data.length : 'not an array');
    }
    return result;
  } catch (error) {
    console.error('listEmails: caught error:', error);
    throw error;
  }
};