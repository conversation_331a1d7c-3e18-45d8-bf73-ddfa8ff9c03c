// @ts-nocheck
import { create } from "zustand";
import { produce } from "immer";
import * as React from 'react';
import * as ReactDOMServer from 'react-dom/server';

import { BannerModule } from '../aura-modules/01-banner-module';
import { HeadingModule } from '../aura-modules/02-heading-module';
import { LogoBannerModule } from '../aura-modules/03-logo-banner';
import { ParagraphModule } from '../aura-modules/05-paragraph';
import { CTAModule } from '../aura-modules/06-cta';
import { CTASecondaryModule } from '../aura-modules/07-cta-secondary';
import { SubHeadingModule } from '../aura-modules/08-sub-heading';
import { HighlightsModule } from "../aura-modules/09-highlights";
import { TwoColumnImages } from "../aura-modules/11-two-column-images";
import { PromoBlock } from "../aura-modules/12-promo";
import { EventDetails } from "../aura-modules/13-event-details";
import { EventDetailsOnline } from "../aura-modules/13-event-details-online";
import { FeaturedSession } from "../aura-modules/14-featured-session";
import EventAgenda from "../aura-modules/15-event-agenda";

function store(set) {
  if (localStorage.getItem("store")) {
    return JSON.parse(localStorage.getItem("store"));
  } else
    return {
      metadata: {
        sender: "Amazon Web Services",
        subject: {
          label: "Subject Line Text",
          value: "Please input your subject line with ~41 characters",
          maxLen: 70,
        },
        preheader: {
          label: "Preheader Text",
          value:
            "Please input your pre-header, this is shown in the inbox and should be max ~85 characters long.",
          maxLen: 100,
        },
        trk: {
          value: "",
          maxLen: 36,
          minLen: 36,
        },
        variant: "english",
        OPER: false,
        RTL: false,
      },
      highlightElement: "",
      elements: [
        {
          id: "H3G7F9A2",
          label: "Banner Header",
          selected: true,
          copy: 0,
          inputs: [
            {
              id: 1,
              label: "Theme",
              customUrl: false,
              dropdown: [
                {
                  label: "Theme A",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/IImg-Gradient_A.jpg"
                },
                {
                  label: "Theme B",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/IImg-Gradient_B.jpg"
                },
                {
                  label: "Theme C",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/IImg-Gradient_C.jpg"
                },
                {
                  label: "Theme D",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/IImg-Gradient_D.jpg"
                }
              ],
              value:
              {
                label: "Theme A",
                value: "https://pages.awscloud.com/rs/112-TZM-766/images/IImg-Gradient_A.jpg"
              },
            },
          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <BannerModule
                moduleId={this.id}
                backgroundSrc={this.inputs[0].value.value}
              />
            );
          }
        },
        {
          id: "B6D8K1S4",
          label: "Title",
          selected: true,
          copy: 0,
          inputs: [
            {
              id: 0,
              label: "Title_Text",
              value: "AmberHeavy Heading <60 characters",
              maxLen: 60,
            },
          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <HeadingModule
                moduleId={this.id}
                headingText={this.inputs[0].value}
              />
            );
          },
        },
        {
          id: "R5P2L9Y7",
          label: "Paragraph",
          selected: true,
          copy: 0,
          inputs: [
            {
              label: "Paragraph_Text",
              value:
                '<a href="http://localhost:3001/#" rel="noopener noreferrer" target="_blank" style="color: rgb(0, 126, 185);">AWS Innovate Online Conference – AI &amp; Machine Learning Edition</a>&nbsp;is back on&nbsp;<strong>February 24, 2022</strong>. This free event is designed to inspire and empower you to accelerate innovation and create new possibilities for tomorrow.. This paragraph should be &lt;250 characters.',
              rte: true,
              maxLen: 280,
              id: 0,
            },
          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <ParagraphModule
                content={this.inputs[0].value}
              />
            );
          },
        },
        {
          id: "X4V6T2N1",
          label: "CTA",
          selected: true,
          copy: 0,
          inputs: [
            {
              id: 0,
              label: "CTA_Text",
              value: "Register now",
            },
            {
              id: 1,
              label: "CTA_URL",
              value:
                "https://aws.amazon.com/events/aws-innovate/machine-learning/online/emea?em_inv_emea22_innovatemlq1&sc_channel=em",
            },
          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <CTAModule
                ctaText={this.inputs[0].value}
                ctaUrl={this.inputs[1].value}
              />
            );
          },
        },
      ],
      optionalElements: [
        {

          label: "Banner Header",
          selected: true,
          copy: 0,
          inputs: [
            // {
            //   id: 0,
            //   label: "Logo_URL",
            //   value:
            //     "https://pages.awscloud.com/rs/112-TZM-766/images/AWS_logo_RGB_WHT_2017_200x120.png",
            // },
            {
              id: 0,
              label: "Theme",
              customUrl: false,
              dropdown: [
                {
                  label: "Theme A",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/IImg-Gradient_A.jpg"
                },
                {
                  label: "Theme B",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/IImg-Gradient_B.jpg"
                },
                {
                  label: "Theme C",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/IImg-Gradient_C.jpg"
                },
                {
                  label: "Theme D",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/IImg-Gradient_D.jpg"
                },
              ],
              value:
              {
                label: "Theme A",
                value: "https://pages.awscloud.com/rs/112-TZM-766/images/IImg-Gradient_A.jpg"
              },
            },
          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <BannerModule
                moduleId={this.id}
                backgroundSrc={this.inputs[0].value.value}
              />
            );
          }
        },

        {
          label: "Banner Hero",
          selected: true,
          copy: 0,
          inputs: [
            {
              id: 0,
              label: "Show AWS Logo",
              dropdown: [
                {
                  label: "Yes",
                  value: "yes"
                },
                {
                  label: "No",
                  value: "no"
                }
              ],
              value: {
                label: "Yes",
                value: "yes"
              }
            },
            {
              id: 1,
              label: "Image URL",
              customUrl: false,
              dropdown: [
                {
                  label: "AWS RE:INVENT re:Cap",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/2025_AWS-reInvent-reCap-email-hero-banner-1200x600.png"
                },
                {
                  label: "AWS Summit Paris",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/Global_GC_T1_aws-summit-paris-core-email header-600x100.png.png?version=1"
                },
                {
                  label: "AWS Summit Amsterdam",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/Global_GC_T1_aws-summit-amsterdam-core-email header-600x100.png.png?version=1"
                },
                {
                  label: "AWS Summit London",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/Global_GC_T1_aws-summit-london-core-email header-600x100.png.png?version=1"
                },
                {
                  label: "AWS Summit Poland",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/Global_GC_T1_aws-summit-poland-core-email header-600x100.png.png?version=1"
                },
                {
                  label: "AWS Summit Dubai",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/Global_GC_T1_aws-summit-dubai-core-email header-600x100.png.png?version=1"
                },
                {
                  label: "AWS Summit Tel Aviv",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/Global_GC_T1_aws-summit-tel-aviv-core-email header-600x100.png.png?version=1"
                },
                {
                  label: "AWS Summit Stockholm",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/Global_GC_T1_aws-summit-stockholm-core-email header-600x100.png.png?version=1"
                },
                {
                  label: "AWS Summit Madrid",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/Global_GC_T1_aws-summit-madrid-core-email header-600x100.png.png?version=1"
                },
                {
                  label: "AWS Summit Hamburg",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/Global_GC_T1_aws-summit-hamburg-core-email header-600x100.png.png?version=1"
                },
                {
                  label: "AWS Summit Milano",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/Global_GC_T1_aws-summit-milano-core-email header-600x100.png.png?version=1"
                },
                {
                  label: "AWS Summit Johannesburg",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/Global_GC_T1_aws-summit-johanesburg-core-email header-600x100.png.png?version=1"
                },
                {
                  label: "AWS Summit Zurich",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/Global_GC_T1_aws-summit-zurich-core-email header-600x100.png.png?version=1"
                },
                {
                  label: "AWS Summit Exec-Leaders",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/Global_GC_T1_aws-summit-exec-leaders-email-banner-600x100.png?version=1"
                },
                {
                  label: "AWS Cloud Day Turkiye",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/2025_Cloud_Days_Turkey_Banner_1200x240.png"
                },
                {
                  label: "AWS Cloud Day Riyadh",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/2025_Cloud_Days_Riyadh_Banner_1200x240.png"
                },
                {
                  label: "AWS Cloud Day Dublin",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/2025_Cloud_Days_Dublin_Banner_1200x240.png"
                },
                {
                  label: "AWS Cloud Day Prague",
                  value: "https://pages.awscloud.com/rs/112-TZM-766/images/2025_Cloud_Days_Prague_Banner_1200x240.png"
                },
                {
                  label: "Custom URL",
                  value: ""
                }

              ],
              value: {
                label: "AWS RE:INVENT re:Cap",
                value: "https://pages.awscloud.com/rs/112-TZM-766/images/2025_AWS-reInvent-reCap-email-hero-banner-1200x600.png"
              },

            },
          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <LogoBannerModule
                // moduleId={this.id}
                ShowLogo={this.inputs[0].value.value}
                bannerSrc={this.inputs[1].value.value}
              />
            );
          },
        },
        // Banner Sub
        // {
        //   label: "Banner Sub",
        //   selected: true,
        //   copy: 0,
        //   inputs: [
        //     {
        //       id: 0,
        //       label: "Date_Time",
        //       value: "October 06, 2022 | 10:00 - 15:00 CEST",
        //     },
        //     {
        //       id: 1,
        //       label: "BG_Color",
        //       value: "#376cac",
        //     },
        //   ],
        //   html: function () {
        //     return `
        //     <table class="mktoModule" id="Hero-Section024a3eb059-339d-42bd-b8b5-ad1f53992344" cellpadding="0" cellspacing="0" align="center" border="0" width="100%" style="margin:0 auto; min-width:100%;"> <tbody> <tr> <td class="darkMode lightMode" bgcolor="#ffffff" style="background-color:#ffffff;"> <table width="100%" cellpadding="0" cellspacing="0" style="border-spacing: 0; mso-table-lspace: 0pt; mso-table-rspace: 0pt; margin:0 auto;width:100%;border-collapse: collapse;" border="0" class="deviceWidth" align="center"> <tbody> <tr> <td class="full-width" valign="top" style="text-align: center;"> <div class="mktoText" id="Hero-Image00001de4ac3f5-d1a8-45b5-a4aa-576a91f27ac3"> <table class="mktoModule pointerCursor" style="border-spacing: 0; border-collapse: collapse; margin: 0px;" align="center" border="0" cellpadding="0" cellspacing="0" width="100%" margin="0px"> <tbody> <tr> <td bgcolor="${this.inputs[1].value}" border="0" style="background-color: ${this.inputs[1].value};"> <table width="540" cellpadding="0" cellspacing="0" style="border-spacing: 0; margin: 0 auto;" border="0" align="center" class="deviceWidth1"> <tbody> <tr> <td valign="top" style="padding: 10px 0px 10px; vertical-align: top; color: #ffffff; font-size: 14px; line-height: 21px; text-align: center; font-family: 'AmazonEmber-Bold',arial, Helvetica, sans-serif; font-weight: normal;"> <p style="margin: 0px;"> ${this.inputs[0].value} </p> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </div> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table>`;
        //   },
        // },
        // Title
        {
          label: "Title",
          selected: true,
          copy: 0,


          inputs: [
            {
              id: 0,
              label: "Title_Text",
              value: "AmberHeavy Heading <60 characters",
              maxLen: 60,
            },
            // {
            //   id: 1,
            //   label: "top_space",
            //   value: 20,
            // },
            // {
            //   id: 2,
            //   label: "bottom_space",
            //   value: 0,
            // },
          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <HeadingModule
                moduleId={this.id}
                headingText={this.inputs[0].value}
              />
            );
          },
        },
        // Paragraph
        {
          label: "Paragraph",
          selected: true,
          copy: 0,

          inputs: [
            {
              label: "Paragraph_Text",
              value:
                '<a href="http://localhost:3001/#" rel="noopener noreferrer" target="_blank" style="color: rgb(0, 126, 185);">AWS Innovate Online Conference – AI &amp; Machine Learning Edition</a>&nbsp;is back on&nbsp;<strong>February 24, 2022</strong>. This free event is designed to inspire and empower you to accelerate innovation and create new possibilities for tomorrow.. This paragraph should be &lt;250 characters.',
              rte: true,
              maxLen: 280,
              id: 0,
            },
            // {
            //   id: 1,
            //   label: "top_space",
            //   value: "20",
            // },
            // {
            //   id: 2,
            //   label: "bottom_space",
            //   value: "0",
            // },
          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <ParagraphModule
                // moduleId={this.id}
                content={this.inputs[0].value}
              />
            );
          },
        },
        // CTA
        {
          label: "CTA",
          selected: true,
          copy: 0,
          inputs: [
            {
              id: 0,
              label: "CTA_Text",
              value: "Register now",
            },
            {
              id: 1,
              label: "CTA_URL",
              value:
                "https://aws.amazon.com/events/aws-innovate/machine-learning/online/emea?em_inv_emea22_innovatemlq1&sc_channel=em",
            },
            // {
            //   id: 2,
            //   label: "top_space",
            //   value: "20",
            // },
            // {
            //   id: 3,
            //   label: "bottom_space",
            //   value: "20",
            // },
          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <CTAModule
                ctaText={this.inputs[0].value}
                ctaUrl={this.inputs[1].value}
              />
            );
          },
        },
        // Secondary CTA
        {
          label: "Secondary CTA",
          selected: true,
          copy: 0,
          inputs: [
            {
              id: 0,
              label: "CTA_Text",
              value: "Learn More",
            },
            {
              id: 1,
              label: "CTA_URL",
              value:
                "https://aws.amazon.com/events/aws-innovate/machine-learning/online/emea?em_inv_emea22_innovatemlq1&sc_channel=em",
            },
            // {
            //   id: 2,
            //   label: "top_space",
            //   value: "20",
            // },
            // {
            //   id: 3,
            //   label: "bottom_space",
            //   value: "20",
            // },
          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <CTASecondaryModule
                ctaText={this.inputs[0].value}
                ctaUrl={this.inputs[1].value}
              />
            );
          },
        },
        // Sub heading
        {
          label: "Sub Heading",
          selected: false,
          copy: 0,
          inputs: [
            {
              id: 0,
              label: "Sub_Heading_Text",
              value: "Event Highlights",
              maxLen: 60,

            },
            // {
            //   id: 1,
            //   label: "top_space",
            //   value: "20",
            // },
            // {
            //   id: 2,
            //   label: "bottom_space",
            //   value: "20",
            // },
          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <SubHeadingModule
                headingText={this.inputs[0].value}
              />
            );
          },
        },
        // Highlights
        {
          label: "Highlights",
          selected: false,
          copy: 0,

          inputs: [
            {
              id: 0,
              keyValuePair: true,
              label: "Highlights List (Max 5)",
              value: [
                {
                  id: 0,
                  title: "Highlight 1 Title",
                  description:
                    '<p style="margin: 0px;">Body: Provide a clear, detailed block of copy that expands upon the title. Include supporting facts. Detailed block of copy that expands upon the title. Include supporting facts. Max 3 lines</p>',

                },
                {
                  id: 1,
                  title: "Highlight 2 Title",
                  description:
                    '<p style="margin: 0px;">Body: Provide a clear, detailed block of copy that expands upon the title. Include supporting facts. Detailed block of copy that expands upon the title. Include supporting facts. Max 3 lines</p>',

                },
                {
                  id: 2,
                  title: "Highlight 3 Title",
                  description:
                    '<p style="margin: 0px;">Body: Provide a clear, detailed block of copy that expands upon the title. Include supporting facts. Detailed block of copy that expands upon the title. Include supporting facts. Max 3 lines</p>',

                },

              ]
            },

          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <HighlightsModule
                highlights={this.inputs[0].value}
              />
            );
          },
        },
        // 2 column images
        {
          label: "2 Column Images",
          selected: false,
          copy: 0,
          inputs: [
            {
              id: 0,
              label: "Column_1_Image_URL",
              value:
                "https://pages.awscloud.com/rs/112-TZM-766/images/Image-Placeholder288x160.png?version=1",
            },
            {
              id: 1,
              label: "Column_1_Title",
              value:
                "Title, sentence case, max 2 line",
            },
            {
              id: 2,
              label: "Column_1_Paragraph",
              value:
                '<div><p style="margin: 0px;">Body: Provide a clear, detailed block of copy that expands upon the title. Include supporting facts.</p></div>',
              rte: true,
              maxLen: 250,
            },
            // {
            //   id: 3,
            //   label: "Column_1_CTA_Text",
            //   value:
            //     "Link CTA",
            // },
            // {
            //   id: 4,
            //   label: "Column_1_CTA_URL",
            //   value:
            //     "#",
            // },
            {
              id: 3,
              label: "Column_2_Image_URL",
              value:
                "https://pages.awscloud.com/rs/112-TZM-766/images/Image-Placeholder288x160.png?version=1",
            },
            {
              id: 4,
              label: "Column_2_Title",
              value:
                "Title, sentence case, max 2 line",
            },
            {
              id: 5,
              label: "Column_2_Paragraph",
              value:
                '<div><p style="margin: 0px;">Body: Provide a clear, detailed block of copy that expands upon the title. Include supporting facts.</p></div>',
              rte: true,
              maxLen: 250,
            },
            // {
            //   id: 8,
            //   label: "Column_2_CTA_Text",
            //   value:
            //     "Link CTA",
            // },
            // {
            //   id: 9,
            //   label: "Column_2_CTA_URL",
            //   value:
            //     "#",
            // },
          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <TwoColumnImages
                cards={[
                  {
                    imageUrl: this.inputs[0].value,
                    title: this.inputs[1].value,
                    description: this.inputs[2].value,
                    // linkText: this.inputs[3].value,
                    // linkUrl: this.inputs[4].value
                  },
                  {
                    imageUrl: this.inputs[3].value,
                    title: this.inputs[4].value,
                    description: this.inputs[5].value,
                    // linkText: this.inputs[8].value,
                    // linkUrl: this.inputs[9].value
                  }
                ]}
              />
            );
          },
        },
        // Promo
        {
          label: "Promo",
          selected: false,
          copy: 0,
          inputs: [
            {
              id: 0,
              label: "Promo_Title",
              value: "AWS Executive highlights from re:Invent 2024",
              maxLen: 60,
            },
            {
              id: 1,
              label: "Promo_Description",
              value:
                '<p style="margin: 0px;">Hear insights from AWS and industry thought leaders on key topics to inspire innovation and transformation including fireside chats with Matt Garman and Swami Sivasubramania</p>',
              rte: true,
              maxLen: 250,
            },
            {
              id: 2,
              label: "Promo_CTA_Text",
              value:
                "Register Now",
            },
            {
              id: 3,
              label: "Promo_CTA_URL",
              value:
                "#",
            },
            // {
            //   id: 2,
            //   label: "top_space",
            //   value: "20",
            // },
            // {
            //   id: 3,
            //   label: "bottom_space",
            //   value: "20",
            // },
            // {
            //   id: 4,
            //   label: "BG_color",
            //   value: "#ccfffd",
            // },
          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <PromoBlock
                title={this.inputs[0].value}
                description={this.inputs[1].value}
                linkText={this.inputs[2].value}
                linkUrl={this.inputs[3].value}
              />
            );
          },
        },
        // Event Details
        {
          label: "Event Details",
          selected: false,
          copy: 0,
          inputs: [
            {
              id: 0,
              label: "Event_Type",
              value: "In-person event",
            },
            {
              id: 1,
              label: "Event_Date_Title",
              // date: true,
              maxLen: 60,
              value:
                'Date',
            },
            {
              id: 2,
              label: "Event_Date_Paragraph",
              rte: true,
              maxLen: 280,
              value:
                "<p>6 March, 2025</p>",
            },
            {
              id: 3,
              label: "Event_Location_Title",
              // date: true,
              maxLen: 60,
              value:
                'Time',
            },
            {
              id: 4,
              label: "Event_Location_Paragraph",
              rte: true,
              maxLen: 280,
              value:
                "<p>09:00 - 14:00 GMT</p><p>10:00 - 15:00 CET</p>",
            },
            // {
            //   id: 3,
            //   label: "Event_End_Time_(24 Hour Format)",
            //   time: true,
            //   value:
            //     "01:30",
            // },
            // {
            //   id: 4,
            //   label: "Event Timezone",
            //   dropdown: [
            //     { "label": "Coordinated Universal Time (UTC)", "value": "UTC" },
            //     { "label": "Greenwich Mean Time (GMT)", "value": "GMT" },
            //     { "label": "Eastern Standard Time (EST)", "value": "EST" },
            //     { "label": "Central Standard Time (CST)", "value": "CST" },
            //     { "label": "Mountain Standard Time (MST)", "value": "MST" },
            //     { "label": "Pacific Standard Time (PST)", "value": "PST" },
            //     { "label": "British Summer Time (BST)", "value": "BST" },
            //     { "label": "Central European Time (CET)", "value": "CET" },
            //     { "label": "Eastern European Time (EET)", "value": "EET" },
            //     { "label": "Indian Standard Time (IST)", "value": "IST" },
            //     { "label": "Japan Standard Time (JST)", "value": "JST" },
            //     { "label": "Korea Standard Time (KST)", "value": "KST" },
            //     { "label": "China Standard Time (CST)", "value": "CST (China)" },
            //     { "label": "Australian Eastern Standard Time (AEST)", "value": "AEST" },
            //     { "label": "New Zealand Standard Time (NZST)", "value": "NZST" },
            //     { "label": "East Africa Time (EAT)", "value": "EAT" },
            //     { "label": "Arabia Standard Time (AST)", "value": "AST" }
            //   ],
            //   value:
            //     { "label": "Coordinated Universal Time (UTC)", "value": "UTC" },
            // },
            // {
            //   id: 5,
            //   label: "Event_Location_Street",
            //   value: "123 St",
            // },
            // {
            //   id: 6,
            //   label: "Event_Location_City",
            //   value: "Seattle, WA",
            // },


          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <EventDetails
                eventType={this.inputs[0].value}
                EventDateTitle={this.inputs[1].value}
                EventDateParagraph={this.inputs[2].value}
                EventLocationTitle={this.inputs[3].value}
                EventLocationParagraph={this.inputs[4].value}
              // locationStreet={this.inputs[5].value}
              // locationCity={this.inputs[6].value}
              />
            );
          },
        },

        // Featured session Image
        {
          label: "Featured Session Image",
          selected: false,
          copy: 0,
          inputs: [
            {
              id: 0,
              label: "Image_URL",
              value:
                "https://pages.awscloud.com/rs/112-TZM-766/images/Innovate_FEA22_Mat-Guimaraes.png",
            },
            {
              id: 1,
              label: "Title_Text",
              value: "Modern Data Strategy",
              maxLen: 60,
            },
            {
              id: 2,
              label: "Subtitle_Text",
              value: "Speaker: Matheus Guimaraes | Level: 300",
              maxLen: 60,
            },
            // {
            //   id: 3,
            //   label: "top_space",
            //   value: "20",
            // },
            // {
            //   id: 4,
            //   label: "bottom_space",
            //   value: "20",
            // },
          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <FeaturedSession
                imageUrl={this.inputs[0].value}
                title={this.inputs[1].value}
                description={this.inputs[2].value}
              />
            );
          },
        },
        // Event Agenda
        {
          label: "Event Agenda",
          selected: false,
          copy: 0,
          inputs: [
            // {
            //   id: 0,
            //   label: "Event Timezone",
            //   dropdown: [
            //     { "label": "Coordinated Universal Time (UTC)", "value": "UTC" },
            //     { "label": "Greenwich Mean Time (GMT)", "value": "GMT" },
            //     { "label": "Eastern Standard Time (EST)", "value": "EST" },
            //     { "label": "Central Standard Time (CST)", "value": "CST" },
            //     { "label": "Mountain Standard Time (MST)", "value": "MST" },
            //     { "label": "Pacific Standard Time (PST)", "value": "PST" },
            //     { "label": "British Summer Time (BST)", "value": "BST" },
            //     { "label": "Central European Time (CET)", "value": "CET" },
            //     { "label": "Eastern European Time (EET)", "value": "EET" },
            //     { "label": "Indian Standard Time (IST)", "value": "IST" },
            //     { "label": "Japan Standard Time (JST)", "value": "JST" },
            //     { "label": "Korea Standard Time (KST)", "value": "KST" },
            //     { "label": "China Standard Time (CST)", "value": "CST (China)" },
            //     { "label": "Australian Eastern Standard Time (AEST)", "value": "AEST" },
            //     { "label": "New Zealand Standard Time (NZST)", "value": "NZST" },
            //     { "label": "East Africa Time (EAT)", "value": "EAT" },
            //     { "label": "Arabia Standard Time (AST)", "value": "AST" }
            //   ],
            //   value:
            //     { "label": "Coordinated Universal Time (UTC)", "value": "UTC" },
            // },
            {
              id: 0,
              label: "Title",
              value: "Event Agenda",
            },
            {
              id: 1,
              label: "Timezone_Column_Header",
              value: "Time (CET)",
            },
            {
              id: 2,
              label: "Sessions_Column_Header",
              value: "Session Name",
            },
            {
              id: 3,
              label: "Agenda Items",
              keyValuePair: true,
              value: [
                {
                  id: "Km5pW3nL",
                  time: "09:00",
                  sessionName: "Opening Keynote"
                },
                {
                  id: "Jf9XsT2q",
                  time: "10:30",
                  sessionName: "Technical Deep Dive"
                },
                {
                  id: "X7vN4hRy",
                  time: "12:00",
                  sessionName: "Lunch Break"
                }
              ]
            },

            // {
            //   id: 3,
            //   label: "top_space",
            //   value: "20",
            // },
            // {
            //   id: 4,
            //   label: "bottom_space",
            //   value: "20",
            // },
          ],
          html: function () {
            return ReactDOMServer.renderToString(
              <EventAgenda
                title={this.inputs[0].value}
                timezone={this.inputs[1].value}
                sessions={this.inputs[2].value}
                agendaItems={this.inputs[3].value}

              />
            );
          },
        },
        // Event Details Online
        // {
        //   label: "Event Details Online",
        //   selected: false,
        //   copy: 0,
        //   inputs: [
        //     {
        //       id: 0,
        //       label: "Event_Type",
        //       value: "Online Event",
        //     },
        //     {
        //       id: 1,
        //       label: "Event_Date",
        //       date: true,
        //       value:
        //         '25/03/2025',
        //     },
        //     {
        //       id: 2,
        //       label: "Event_Start_Time_(24 Hour Format)",
        //       time: true,
        //       value:
        //         "12:00",
        //     },
        //     {
        //       id: 3,
        //       label: "Event_End_Time_(24 Hour Format)",
        //       time: true,
        //       value:
        //         "01:30",
        //     },
        //     {
        //       id: 4,
        //       label: "Event Timezone",
        //       dropdown: [
        //         { "label": "Coordinated Universal Time (UTC)", "value": "UTC" },
        //         { "label": "Greenwich Mean Time (GMT)", "value": "GMT" },
        //         { "label": "Eastern Standard Time (EST)", "value": "EST" },
        //         { "label": "Central Standard Time (CST)", "value": "CST" },
        //         { "label": "Mountain Standard Time (MST)", "value": "MST" },
        //         { "label": "Pacific Standard Time (PST)", "value": "PST" },
        //         { "label": "British Summer Time (BST)", "value": "BST" },
        //         { "label": "Central European Time (CET)", "value": "CET" },
        //         { "label": "Eastern European Time (EET)", "value": "EET" },
        //         { "label": "Indian Standard Time (IST)", "value": "IST" },
        //         { "label": "Japan Standard Time (JST)", "value": "JST" },
        //         { "label": "Korea Standard Time (KST)", "value": "KST" },
        //         { "label": "China Standard Time (CST)", "value": "CST (China)" },
        //         { "label": "Australian Eastern Standard Time (AEST)", "value": "AEST" },
        //         { "label": "New Zealand Standard Time (NZST)", "value": "NZST" },
        //         { "label": "East Africa Time (EAT)", "value": "EAT" },
        //         { "label": "Arabia Standard Time (AST)", "value": "AST" }
        //       ],
        //       value:
        //         { "label": "Coordinated Universal Time (UTC)", "value": "UTC" },
        //     },
        //     {
        //       id: 5,
        //       label: "Virtual_Link_Text",
        //       value: "Link",
        //     },
        //     {
        //       id: 6,
        //       label: "Virtual_Link_URL",
        //       value: "#",
        //     },


        //   ],
        //   html: function () {
        //     return ReactDOMServer.renderToString(
        //       <EventDetailsOnline
        //         eventType={this.inputs[0].value}
        //         date={this.inputs[1].value}
        //         startTime={this.inputs[2].value}
        //         endTime={this.inputs[3].value}
        //         timezone={this.inputs[4].value.value}
        //         virtualLinkText={this.inputs[5].value}
        //         virtualLink={this.inputs[6].value}
        //       />
        //     );
        //   },
        // },
        // 4 Column Icons
        // {
        //   label: "4 Column Icons",
        //   selected: false,
        //   copy: 0,
        //   inputs: [
        //     {
        //       id: 0,
        //       label: "Icon_1_URL",
        //       value:
        //         "https://pages.awscloud.com/rs/112-TZM-766/images/aws-email-icon-Cloud-t4.png",
        //       theme: "t1",
        //       type: "Sessions",
        //     },
        //     {
        //       id: 1,
        //       label: "Paragraph_1_Text",
        //       value:
        //         '<div><p style="margin: 0px;"> chat with aws experts </p></div>',
        //       rte: true,
        //       maxLen: 250,
        //     },
        //     {
        //       id: 2,
        //       label: "Icon_2_URL",
        //       value:
        //         "https://pages.awscloud.com/rs/112-TZM-766/images/aws-email-icon-Chat-t1.png",
        //       theme: "t1",
        //       type: "Chat",
        //     },
        //     {
        //       id: 3,
        //       label: "Paragraph 2 Text",
        //       value:
        //         '<div><p style="margin: 0px;"> chat with aws experts </p></div>',
        //       rte: true,
        //       maxLen: 250,
        //     },
        //     {
        //       id: 4,
        //       label: "Icon_3_URL",
        //       value:
        //         "https://pages.awscloud.com/rs/112-TZM-766/images/aws-email-icon-Graphs-t1.png",
        //       theme: "t1",
        //       type: "Graphs",
        //     },
        //     {
        //       id: 5,
        //       label: "Paragraph_3_Text",
        //       value:
        //         '<div><p style="margin: 0px;"> chat with aws experts </p></div>',
        //       rte: true,
        //       maxLen: 250,
        //     },
        //     {
        //       id: 6,
        //       label: "Icon_4_URL",
        //       value:
        //         "https://pages.awscloud.com/rs/112-TZM-766/images/aws-email-icon-Data-t1.png",
        //       theme: "t1",
        //       type: "Data",
        //     },
        //     {
        //       id: 7,
        //       label: "Paragraph_4_Text",
        //       value:
        //         '<div><p style="margin: 0px;">chat with aws experts </p></div>',
        //       rte: true,
        //       maxLen: 250,
        //     },
        //     {
        //       id: 8,
        //       label: "top_space",
        //       value: "20",
        //     },
        //     {
        //       id: 9,
        //       label: "bottom_space",
        //       value: "20",
        //     },
        //   ],
        //   html: function () {
        //     return `
        //     <table class="mktoModule" id="content-w-headlinee15050ef-5435-470b-82eb-e2be452a25a9" cellpadding="0" cellspacing="0" align="center" border="0" width="100%" style="margin:0 auto; min-width:100%;"> <tbody> <tr> <td class="darkMode lightMode" bgcolor="#FFFFFF" style="background-color:#ffffff;"> <table width="552" cellpadding="0" cellspacing="0" style="border-spacing: 0; mso-table-lspace: 0pt; mso-table-rspace: 0pt; margin:0 auto;width: 552px;border-collapse: collapse;" border="0" class="deviceWidth1" align="center"> <tbody> <tr> <td height="${this.inputs[8].value}px" style="line-height:1px;font-size:1px;"> &nbsp;</td> </tr> <tr> <td class="white dark" valign="top" style="vertical-align:top;color:#232F3E;font-size:14px;line-height:22px;text-align:left;font-family: AmazonEmber,arial, Helvetica, sans-serif;font-weight:normal;"> <div class="mktoText" id="content-wid-titleba51d2a3-60e1-462e-b1d2-676472884597"> <table align="center" width="100%" cellpadding="0" cellspacing="0" border="0"> <tbody> <tr> <th class="block deviceWidth" width="260" valign="top" style="text-align: left; font-weight: 400;"> <table align="center" width="100%" cellpadding="0" cellspacing="0" border="0"> <tbody> <tr> <th width="100" valign="top" style="text-align: left; font-weight: 400;"> <table align="center" width="100%" cellpadding="0" cellspacing="0" border="0"> <tbody> <tr> <td align="center"> <img src="${this.inputs[0].value}" width="100" style="max-width: 100px;" constrain="true" imagepreview="false" height="auto"> </td> </tr> <tr> <td height="15" style="font-size: 1px; line-height: 1px;"> &nbsp; </td> </tr> <tr> <td valign="top" style="vertical-align: top; font-size: 14px; line-height: 20px; font-family: 'AmazonEmber-Heavy',arial,helvetica; font-weight: normal; text-align: center;"> <strong>${this.inputs[1].value}</strong> </td> </tr> <tr> <td height="12" style="font-size: 1px; line-height: 1px;"> &nbsp; </td> </tr> </tbody> </table> </th> <th width="15" height="20" style="font-size: 1px; line-height: 1px;"> &nbsp; </th> <th width="100" valign="top" style="text-align: left; font-weight: 400;"> <table align="center" width="100%" cellpadding="0" cellspacing="0" border="0"> <tbody> <tr> <td align="center"> <img src="${this.inputs[2].value}" width="100" style="max-width: 100px;" constrain="true" imagepreview="false" height="auto"> </td> </tr> <tr> <td height="13" style="font-size: 1px; line-height: 1px;"> &nbsp; </td> </tr> <tr> <td valign="top" style="vertical-align: top; font-size: 14px; line-height: 20px; font-family: 'AmazonEmber-Heavy',arial,helvetica; font-weight: normal; text-align: center;"> <strong>${this.inputs[3].value}</strong> </td> </tr> <tr> <td height="12" style="font-size: 1px; line-height: 1px;"> &nbsp; </td> </tr> <tr> <td valign="top"> </td> </tr> </tbody> </table> </th> </tr> </tbody> </table> </th> <th class="block" width="15" height="20" style="font-size: 1px; line-height: 1px;"> &nbsp;</th> <th class="block deviceWidth" valign="top" style="text-align: left; font-weight: 400;"> <table align="center" width="100%" cellpadding="0" cellspacing="0" border="0"> <tbody> <tr> <th width="100" valign="top" style="text-align: left; font-weight: 400;"> <table align="center" width="100%" cellpadding="0" cellspacing="0" border="0"> <tbody> <tr> <td class="full-width" align="center"> <img src="${this.inputs[4].value}" width="100" style="max-width: 100px;" constrain="true" imagepreview="false" height="auto"> </td> </tr> <tr> <td height="13" style="font-size: 1px; line-height: 1px;"> &nbsp; </td> </tr> <tr> <td valign="top" style="vertical-align: top; font-size: 14px; line-height: 20px; font-family: 'AmazonEmber-Heavy',arial,helvetica; font-weight: normal; text-align: center;"> <strong>${this.inputs[5].value}</strong> </td> </tr> <tr> <td height="12" style="font-size: 1px; line-height: 1px;"> &nbsp; </td> </tr> </tbody> </table> </th> <th width="15" height="20" style="font-size: 1px; line-height: 1px;"> &nbsp; </th> <th width="100" valign="top" style="text-align: left; font-weight: 400;"> <table align="center" width="100%" cellpadding="0" cellspacing="0" border="0"> <tbody> <tr> <td class="full-width" align="center"> <img src="${this.inputs[6].value}" width="100" style="max-width: 100px;" constrain="true" imagepreview="false" height="auto"> </td> </tr> <tr> <td height="13" style="font-size: 1px; line-height: 1px;"> &nbsp; </td> </tr> <tr> <td valign="top" style="vertical-align: top; font-size: 14px; line-height: 20px; font-family: 'AmazonEmber-Heavy',arial,helvetica; font-weight: normal; text-align: center;"> <strong>${this.inputs[7].value}</strong> </td> </tr> <tr> <td height="12" style="font-size: 1px; line-height: 1px;"> &nbsp; </td> </tr> </tbody> </table> </th> </tr> </tbody> </table> </th> </tr> </tbody> </table> </div> </td> </tr> <tr> <td height="${this.inputs[9].value}px" style="line-height:1px;font-size:1px;"> &nbsp;</td> </tr> </tbody> </table> </td> </tr> </tbody> </table>`;
        //   },
        // },
        // 2 column icons
        // {
        //   label: "2 Column Icons",
        //   selected: false,
        //   copy: 0,
        //   inputs: [
        //     {
        //       id: 0,
        //       label: "Icon_1_URL",
        //       value:
        //         "https://pages.awscloud.com/rs/112-TZM-766/images/aws-email-icon-Sessions-t1.png",
        //       theme: "t1",
        //       type: "Sessions",
        //     },
        //     {
        //       id: 1,
        //       label: "Paragraph_1_Text",
        //       value:
        //         '<div><p style="margin: 0px;"> <strong>Grab an AWS Swag Box!</strong> </p><p style="margin: 0px;"> Register for AWS Summit and opt-in and connect with AWS Partners. T&amp;C Conditions apply. This paragraph should be &lt;150 characters. </p></div>',
        //       rte: true,
        //       maxLen: 250,
        //     },
        //     {
        //       id: 2,
        //       label: "Icon_2_URL",
        //       value:
        //         "https://pages.awscloud.com/rs/112-TZM-766/images/aws-email-icon-Chat-t1.png",
        //       theme: "t1",
        //       type: "Chat",
        //     },
        //     {
        //       id: 3,
        //       label: "Paragraph 2 Text",
        //       value:
        //         '<div><p style="margin: 0px;"> <strong>AWS Cloud Exam Discount</strong> </p><p style="margin: 0px;"> Register for AWS Summit and opt-in and connect with AWS Partners. T&amp;C Conditions apply. This paragraph should be &lt;150 characters. </p></div>',
        //       rte: true,
        //       maxLen: 250,
        //     },
        //     {
        //       id: 4,
        //       label: "top_space",
        //       value: "20",
        //     },
        //     {
        //       id: 5,
        //       label: "bottom_space",
        //       value: "20",
        //     },
        //   ],
        //   html: function () {
        //     return `<table class="mktoModule" id="content-w-headlinec6df755c-f415-4b35-848e-80945ca36b6e" cellpadding="0" cellspacing="0" align="center" border="0" width="100%" style="margin:0 auto; min-width:100%;"> <tbody> <tr> <td class="darkMode lightMode" bgcolor="#FFFFFF" style="background-color:#ffffff;"> <table width="552" cellpadding="0" cellspacing="0" style="border-spacing: 0; mso-table-lspace: 0pt; mso-table-rspace: 0pt; margin:0 auto;width: 552px;border-collapse: collapse;" border="0" class="deviceWidth1" align="center"> <tbody> <tr> <td height="${this.inputs[4].value}px" style="line-height:1px;font-size:1px;"> &nbsp;</td> </tr> <tr> <td class="white dark" valign="top" style="vertical-align:top;color:#232F3E;font-size:14px;line-height:22px;text-align:left;font-family: AmazonEmber,arial, Helvetica, sans-serif;font-weight:normal;"> <div class="mktoText" id="content-wid-title924413de-897f-4b8a-b456-c1d648c6bde8"> <table align="center" width="100%" cellpadding="0" cellspacing="0" border="0"> <tbody> <tr> <td> <table align="center" width="100%" cellpadding="0" cellspacing="0" border="0"> <tbody> <tr> <th class="block deviceWidth" width="165" valign="top" style="text-align: left; font-weight: 400;"> <table align="center" width="100%" cellpadding="0" cellspacing="0" border="0"> <tbody> <tr> <td class="center" align="left" style="padding: 20px 0px 0px;"> <img src="${this.inputs[0].value}" width="90" style="max-width: 90px;" constrain="true" imagepreview="false"> </td> </tr> <tr> <td valign="top" style="padding: 20px 0px 0px; vertical-align: top; color: #232f3e; font-size: 14px; line-height: 22px; text-align: left; font-family: AmazonEmber,arial, Helvetica, sans-serif; font-weight: normal;"> <p style="margin: 0px;"> ${this.inputs[1].value} </p> </td> </tr> </tbody> </table> </th> <th class="block" width="20" height="10" style="font-size: 1px; line-height: 1px;"> &nbsp; </th> <th class="block deviceWidth" width="165" valign="top" style="text-align: left; font-weight: 400;"> <table align="center" width="100%" cellpadding="0" cellspacing="0" border="0"> <tbody> <tr> <td class="center" align="left" style="padding: 20px 0px 0px;"> <img src="${this.inputs[2].value}" width="90" style="max-width: 90px;" constrain="true" imagepreview="false"> </td> </tr> <tr> <td valign="top" style="padding: 20px 0px 0px; vertical-align: top; color: #232f3e; font-size: 14px; line-height: 22px; text-align: left; font-family: AmazonEmber,arial, Helvetica, sans-serif; font-weight: normal;"> <p style="margin: 0px;">${this.inputs[3].value} </p> </td> </tr> </tbody> </table> </th> </tr> </tbody> </table> </td> </tr> </tbody> </table> </div> </td> </tr> <tr> <td height="${this.inputs[5].value}px" style="line-height:1px;font-size:1px;"> &nbsp;</td> </tr> </tbody> </table> </td> </tr> </tbody> </table>`;
        //   },
        // },

        // Calender
        // {
        //   label: "Calendar",
        //   selected: false,
        //   copy: 0,
        //   inputs: [
        //     {
        //       id: 0,
        //       label: "Date",
        //       value: "2023-09-30",
        //       date: true,
        //     },
        //     {
        //       id: 1,
        //       label: "Title_Text",
        //       value: "AWS Innovate AIML Edition",
        //       maxLen: 60,
        //     },
        //     {
        //       id: 2,
        //       label: "Time",
        //       value: "10:00 - 15:00 CEST",
        //       maxLen: 60,
        //     },
        //     {
        //       id: 3,
        //       label: "Location_Text",
        //       value: "Event Venue",
        //       maxLen: 60,
        //     },
        //     {
        //       id: 4,
        //       label: "Location_URL",
        //       value: "#",
        //     },
        //     {
        //       id: 5,
        //       label: "top_space",
        //       value: "20",
        //     },
        //     {
        //       id: 6,
        //       label: "bottom_space",
        //       value: "20",
        //     },
        //   ],
        //   html: function () {
        //     return `
        //     <table class="mktoModule" id="content-w-headline9860cd9d-46ed-4313-a055-8c114923e7eb" cellpadding="0" cellspacing="0" align="center" border="0" width="100%" style="margin:0 auto; min-width:100%;"> <tbody> <tr> <td class="darkMode lightMode" bgcolor="#FFFFFF" style="background-color:#ffffff;"> <table width="552" cellpadding="0" cellspacing="0" style="border-spacing: 0; mso-table-lspace: 0pt; mso-table-rspace: 0pt; margin:0 auto;width: 552px;border-collapse: collapse;" border="0" class="deviceWidth1" align="center"> <tbody> <tr> <td height="${this.inputs[5].value
        //       }px" style="line-height:1px;font-size:1px;"> &nbsp;</td> </tr> <tr> <td class="white dark" valign="top" style="vertical-align:top;color:#232F3E;font-size:14px;line-height:22px;text-align:left;font-family: AmazonEmber,arial, Helvetica, sans-serif;font-weight:normal;"> <div class="mktoText" id="content-wid-title329151bc-bc41-4952-b45b-71b9da92beb3"> <table align="center" width="100%" cellpadding="0" cellspacing="0" border="0"> <tbody> <tr> <th class="block deviceWidth center" width="140" valign="top" style="text-align: left; font-weight: 400;"> <table class="widthAuto" align="center" width="100%" cellpadding="0" cellspacing="0" border="0" style="margin-top: 0px;"> <tbody> <tr> <td bgcolor="#232F3E" class="fontBold" style="font-family: 'AmazonEmber-Bold',arial,helvetica; font-size: 14px; line-height: 20px; text-align: center; font-weight: normal; color: #ffffff; mso-line-height-rule: exactly; padding: 3px 15px; letter-spacing: 2px; text-transform: uppercase;"> <div> ${new Date(
        //         this.inputs[0].value
        //       ).toLocaleString("default", {
        //         month: "long",
        //       })} </div> </td> </tr> <tr> <td class="calTableRes white" bgcolor="#FEFEFE" style="font-family: 'AmazonEmber-Bold',arial,helvetica; font-size: 60px; line-height: 80px; text-align: center; font-weight: normal; color: #232f3e; mso-line-height-rule: exactly; padding: 13px 10px;"> <div style="color: #232f3e;"> ${new Date(
        //         this.inputs[0].value
        //       ).getDate()} </div> </td> </tr> </tbody> </table> </th> <th class="block" width="20" height="15" style="font-size: 1px; line-height: 1px;"> &nbsp;</th> <th class="block deviceWidth" valign="top" style="text-align: left; font-weight: 400;"> <table align="center" width="100%" cellpadding="0" cellspacing="0" border="0" style="margin-top: 0px;"> <tbody> <tr> <td class="fontBold" style="font-family: 'AmazonEmber-Heavy',arial,helvetica; font-size: 14px; line-height: 22px; font-weight: normal; color: #232f3e; mso-line-height-rule: exactly; text-align: left;"> <strong>${this.inputs[1].value
        //       }</strong> </td> </tr> <tr> <td style="padding: 8px 0 0;"> <table cellpadding="0" cellspacing="0" border="0" width="100%"> <tbody> <tr> <td width="20" valign="top" style="font-size: 0; line-height: 22px; padding-top: 3px; text-align: left;"> <img src="https://pages.awscloud.com/rs/112-TZM-766/images/calIcon121.png" width="18"> </td> <td valign="middle" style="font-family: 'AmazonEmber',arial,helvetica; font-size: 14px; line-height: 22px; font-weight: normal; color: #232f3e; mso-line-height-rule: exactly; text-align: left; padding-left: 10px;"> ${this.inputs[2].value
        //       } </td> </tr> </tbody> </table> </td> </tr> <tr> <td style="padding: 8px 0 0;"> <table cellpadding="0" cellspacing="0" border="0" width="100%"> <tbody> <tr> <td width="20" valign="top" style="font-size: 0; line-height: 22px; padding-top: 3px; text-align: left;"> <img src="https://pages.awscloud.com/rs/112-TZM-766/images/loc_icon-01.png" width="18"> </td> <td valign="middle" style="font-family: 'AmazonEmber',arial,helvetica; font-size: 14px; line-height: 22px; font-weight: normal; color: #232f3e; mso-line-height-rule: exactly; text-align: left; padding-left: 10px;"> <p style="margin: 0px;"> <a href="${this.inputs[4].value
        //       }" style="text-decoration: none; color: #0972d3;">${this.inputs[3].value
        //       }</a> </p> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </th> </tr> </tbody> </table> </div> </td> </tr> <tr> <td height="${this.inputs[6].value
        //       }px" style="line-height:1px;font-size:1px;"> &nbsp;</td> </tr> </tbody> </table> </td> </tr> </tbody> </table>`;
        //   },
        // },

        // Featured seession text
        // {
        //   label: "Featured Session Text",
        //   selected: false,
        //   copy: 0,
        //   inputs: [
        //     {
        //       id: 0,
        //       label: "Title_Text",
        //       value: "Modern Data Strategy",
        //       maxLen: 60,
        //     },
        //     {
        //       id: 1,
        //       label: "Subtitle_Text",
        //       value: "Speaker: Matheus Guimaraes | Level: 300",
        //       maxLen: 60,
        //     },
        //     {
        //       id: 2,
        //       label: "top_space",
        //       value: "20",
        //     },
        //     {
        //       id: 3,
        //       label: "bottom_space",
        //       value: "20",
        //     },
        //   ],
        //   html: function () {
        //     return `
        //     <table class="mktoModule" id="content-w-headlinebae9c4bc-5f9f-4c93-81fa-1cd492df21fc" cellpadding="0" cellspacing="0" align="center" border="0" width="100%" style="margin:0 auto; min-width:100%;"> <tbody> <tr> <td class="darkMode lightMode" bgcolor="#FFFFFF" style="background-color:#ffffff;"> <table width="552" cellpadding="0" cellspacing="0" style="border-spacing: 0; mso-table-lspace: 0pt; mso-table-rspace: 0pt; margin:0 auto;width: 552px;border-collapse: collapse;" border="0" class="deviceWidth1" align="center"> <tbody> <tr> <!-- Height Specified via variable --> <td height="${this.inputs[2].value}px" style="line-height:1px;font-size:1px;"> &nbsp;</td> </tr> <tr> <td class="white dark" valign="top" style="vertical-align:top;color:#232F3E;font-size:14px;line-height:22px;text-align:left;font-family: AmazonEmber,arial, Helvetica, sans-serif;font-weight:normal;"> <div class="mktoText" id="content-wid-title7feb29b2-df54-4202-bafb-e6380932438e"> <table class="mktoModule pointerCursor" align="center" width="100%" cellpadding="0" cellspacing="0" style="background-repeat: no-repeat; background-position: center; background-size: cover; border: none; padding: 20px; background-color: #fcfcfc;" bgcolor="#FCFCFC"> <tbody> <tr> <td valign="middle" style="vertical-align: top; color: #232f3e; font-size: 14px; line-height: 22px; font-family: 'AmazonEmber',arial,helvetica; font-weight: normal;" class="mobile-center"> <p style="margin: 0px;"> <strong>${this.inputs[0].value}</strong> <br>${this.inputs[1].value}</p> </td> </tr> </tbody> </table> </div> </td> </tr> <tr> <td height="${this.inputs[3].value}px" style="line-height:1px;font-size:1px;"> &nbsp;</td> </tr> </tbody> </table> </td> </tr> </tbody> </table>`;
        //   },
        // },



      ],
      toggleElement: (id) =>
        set(
          produce((state) => {

            const element = state.elements.find(el => el.id === id);
            if (element) {
              element.selected = !element.selected;
              console.log("TOGGLED IN STORE **** ", element.selected)
            }
          })
        ),

      editValue: (id1, id2, value, optionalPropLabel) =>
        set(
          produce((state) => {

            const module = state.elements.find(el => el.id === id1)
            if (module) {
              const moduleInput = module.inputs.find(inp => inp.id === id2);
              if (optionalPropLabel) {
                if ("customUrl" in moduleInput) moduleInput.customUrl = value;
                return;
              }
              if (moduleInput && moduleInput.value !== value) { // Add value check
                moduleInput.value = value;
              }
            }



            // localStorage.setItem("store", JSON.stringify(state));
          })
        ),

      reorderState: (reorderedElements) =>
        set(
          produce((state) => {
            // console.log("incoming ELEMENTS **** ", reorderedElements)
            state.elements = reorderedElements;
            console.log("REORDERED ELEMENTS **** ", state.elements)
          })
        ),

      addModule: (newModule) =>
        set(
          produce((state) => {
            state.elements.push(newModule);
          })
        ),
      editMetadata: (key, value) =>
        set(
          produce((state) => {
            if (typeof state.metadata[key] === 'object' && state.metadata[key] !== null && !Array.isArray(state.metadata[key])) {
              state.metadata[key].value = value;
            } else {

              state.metadata[key] = value;
            }

          })
        ),
      resetMetadata: (value) =>
        set(
          produce((state) => {

            state.metadata = { ...value };

            console.log(state.metadata)
          })
        ),
      deleteModule: (id) =>
        set(
          produce((state) => {
            const index = state.elements.findIndex(el => el.id === id);
            if (index !== -1) {
              state.elements.splice(index, 1);
            }
          })
        ),
      editHighlightEl: (value) =>
        set(
          produce((state) => {
            state.highlightElement = value;
          })
        ),
      resetToDefault: () =>
        set(
          produce((state) => {
            // Reset to initial state
            if (localStorage.getItem("defaultStore")) {
              const defaultState = JSON.parse(localStorage.getItem("defaultStore"));
              state.elements = defaultState.elements;
              state.metadata = defaultState.metadata;
              state.highlightElement = "";
            } else {
              // If no default store exists, reset to the initial state defined in the store function
              const initialState = store(set);
              state.elements = initialState.elements;
              state.metadata = initialState.metadata;
              state.highlightElement = "";
            }
          })
        ),
    };
}

export const useStore = create(store);
