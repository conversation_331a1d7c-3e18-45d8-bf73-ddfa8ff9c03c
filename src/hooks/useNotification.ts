import { useState, useCallback } from 'react';

type NotificationType = 'success' | 'error' | 'warning' | 'info';

interface NotificationItem {
  type: NotificationType;
  content: string;
  id: string;
  dismissible?: boolean;
  onDismiss?: () => void;
}

export const useNotification = () => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);

  const addNotification = useCallback((type: NotificationType, content: string, options: { dismissible?: boolean; onDismiss?: () => void } = {}) => {
    const id = Math.random().toString(36).substr(2, 9);
    const notification: NotificationItem = {
      type,
      content,
      id,
      dismissible: options.dismissible ?? true,
      onDismiss: options.onDismiss
    };

    setNotifications(prev => [...prev, notification]);

    // Auto-dismiss after 5 seconds for non-error notifications
    if (type !== 'error') {
      setTimeout(() => {
        dismissNotification(id);
      }, 5000);
    }

    return id;
  }, []);

  const dismissNotification = useCallback((id: string) => {
    setNotifications(prev => {
      const notification = prev.find(n => n.id === id);
      if (notification?.onDismiss) {
        notification.onDismiss();
      }
      return prev.filter(n => n.id !== id);
    });
  }, []);

  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const getFlashbarItems = useCallback(() => {
    return notifications.map(notification => ({
      type: notification.type,
      content: notification.content,
      dismissible: notification.dismissible,
      onDismiss: notification.dismissible ? () => dismissNotification(notification.id) : undefined,
      id: notification.id
    }));
  }, [notifications, dismissNotification]);

  return {
    notifications,
    addNotification,
    dismissNotification,
    clearAllNotifications,
    getFlashbarItems
  };
};