import { useState, useCallback } from 'react';
import { EmailData, APIResponse } from '../interfaces/email';
import * as emailAPI from '../services/api';

interface UseEmailReturn {
  isLoading: boolean;
  error: string | null;
  emailList: Array<{ label: string; value: string }>;
  createEmail: (data: EmailData) => Promise<APIResponse>;
  getEmail: (campaignId: string) => Promise<APIResponse>;
  updateEmail: (campaignId: string, data: EmailData) => Promise<APIResponse>;
  deleteEmail: (campaignId: string) => Promise<APIResponse>;
  listEmails: (emailOwner: string) => Promise<APIResponse>;
}

export const useEmail = (): UseEmailReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [emailList, setEmailList] = useState<Array<{ label: string; value: string }>>([]);

  const handleAPICall = async <T extends any[]>(
    apiFunction: (...args: T) => Promise<APIResponse>,
    ...args: T
  ): Promise<APIResponse> => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('handleAPICall: calling apiFunction', apiFunction.name, 'with args:', args);
      const response = await apiFunction(...args);
      console.log('useEmail: received response', response);
      
      if (response.error) {
        console.log('useEmail: error in response', response.error);
        setError(response.error.message);
      }
      return response;
    } catch (err) {
      console.error('useEmail: caught error', err);
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      return {
        statusCode: 500,
        error: { message: errorMessage }
      };
    } finally {
      setIsLoading(false);
    }
  };

  const create = useCallback(
    async (data: EmailData) => handleAPICall(emailAPI.createEmail, data),
    []
  );

  const get = useCallback(
    async (campaignId: string) => handleAPICall(emailAPI.getEmail, campaignId),
    []
  );

  const update = useCallback(
    async (campaignId: string, data: EmailData) => 
      handleAPICall(emailAPI.updateEmail, campaignId, data),
    []
  );

  const remove = useCallback(
    async (campaignId: string) => handleAPICall(emailAPI.deleteEmail, campaignId),
    []
  );

  const list = useCallback(
    async (emailOwner: string) => {
      console.log('useEmail.list: calling listEmails with emailOwner:', emailOwner);
      const response = await handleAPICall(emailAPI.listEmails, emailOwner);
      console.log('useEmail.list: received response from handleAPICall:', response);
      console.log('useEmail.list: response structure:', JSON.stringify(response, null, 2));
      
      // Check for data property first (from the actual API response)
      if (response && Array.isArray(response)) {
        console.log('useEmail.list: response is an array with length:', response.length);
        // Transform API response to Select component format
        const formattedEmailList = response.map((email: any) => {
          console.log('useEmail.list: processing email item:', email);
          return {
            label: email.emailName || email.campaignId || 'Untitled Email',
            value: email.campaignId || email.id
          };
        });
        console.log('useEmail.list: formatted email list:', formattedEmailList);
        setEmailList(formattedEmailList);
      }
      // Fallback to body property (from the previous implementation)
      else if (response.body && Array.isArray(response.body)) {
        console.log('useEmail.list: response.body is an array with length:', response.body.length);
        // Transform API response to Select component format
        const formattedEmailList = response.body.map((email: any) => {
          console.log('useEmail.list: processing email item:', email);
          return {
            label: email.emailName || email.campaignId || 'Untitled Email',
            value: email.campaignId || email.id
          };
        });
        console.log('useEmail.list: formatted email list:', formattedEmailList);
        setEmailList(formattedEmailList);
      } else {
        console.warn('useEmail.list: neither response nor response.body is a valid array');
        console.warn('useEmail.list: response properties:', Object.keys(response));
        // If neither data nor body is an array, set emailList to empty array
        setEmailList([]);
      }
      return response;
    },
    []
  );

  return {
    isLoading,
    error,
    emailList,
    createEmail: create,
    getEmail: get,
    updateEmail: update,
    deleteEmail: remove,
    listEmails: list
  };
};