// API Configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'https://u6vlwj5lth.execute-api.eu-west-2.amazonaws.com/prod',
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
};

// Email Types
export const EMAIL_TYPES = {
  MARKETING: { label: 'Marketing', value: 'marketing' },
  OPER: { label: 'OPER', value: 'oper' }
};

// Language Options
export const LANGUAGE_OPTIONS = [
  { label: 'english', value: 'english' },
  { label: 'french', value: 'french' },
  { label: 'spanish', value: 'spanish' },
  { label: 'german', value: 'german' },
  { label: 'italian', value: 'italian' },
  { label: 'portuguese', value: 'portuguese' },
  { label: 'turkish', value: 'turkish' },
  { label: 'audience segment', value: 'audience segment' }
];

// Validation
export const VALIDATION = {
  TRK_REGEX: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
  FILE_NAME_PATTERN: /^[a-zA-Z0-9-]+$/,
  MAX_FILE_NAME_LENGTH: 50
};

// UI Constants
export const UI_CONSTANTS = {
  EMAIL_PREVIEW_WIDTH: '680px',
  LOADING_OVERLAY_OPACITY: 0.9,
  MODAL_SIZES: {
    SMALL: 'small',
    MEDIUM: 'medium',
    LARGE: 'large'
  }
};

// Template Options
export const TEMPLATE_OPTIONS = [
  {
    label: 'Best Performing Templates',
    disabled: true,
    options: Array.from({ length: 5 }, (_, i) => ({
      label: `Template ${i + 1}`,
      value: `email_template_${i + 1}`
    }))
  }
];

// Error Messages
export const ERROR_MESSAGES = {
  INVALID_FILE_FORMAT: 'Please import JSON file to proceed',
  REQUIRED_FILE_NAME: 'File name is required',
  INVALID_TRK: 'Please enter a valid TRK code to export this file',
  REQUIRED_EMAIL_NAME: 'Email name is required',
  REQUIRED_EMAIL_OWNER: 'Email owner is required'
};