import React, { useEffect, useState } from 'react';
import { useStore } from './data/store';
import { CampaignsApiEndpoint } from './config';
import ServiceHomepage from './components/home';

// Main App component
const App: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isCurrentUserOwner, setIsCurrentUserOwner] = useState(true);
  const mapModulesToHTML = useStore(state => state.mapModulesToHTML);
  const setSelectedCampaignId = useStore(state => state.setSelectedCampaignId);
  const setEmailData = useStore(state => state.setEmailData);

  useEffect(() => {
    // Function to parse path parameters
    const getPathParams = () => {
      // Get emailId from URL path (last segment)
      const pathSegments = window.location.pathname.split('/').filter(segment => segment);
      const emailId = pathSegments[pathSegments.length - 1];

      // Check if emailId looks like a campaign ID (contains underscores and is not 'maverick')
      return emailId && emailId !== 'maverick' && emailId.includes('_') ? emailId : null;
    };

    // Function to load email by ID
    const loadEmailFromParams = async () => {
      const emailId = getPathParams();

      if (emailId) {
        setIsLoading(true);

        try {
          // API Configuration
          const API_KEY = 'nutbLu8oMw6w9aVuw5h811QzVsSeWwJt3dJvwMX5';
          const headers = {
            'x-api-key': API_KEY,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          };

          // Fetch the email
          const response = await window.fetch(`${CampaignsApiEndpoint}/emails/${emailId}`, {
            method: 'GET',
            headers: headers
          });

          if (response.ok) {
            const data = await response.json();

            if (data.body && data.body.data) {
              // Get current user
              const user = window.harmony.api.getUser();
              const currentUserLogin = user.login;

              // Check if current user is the owner
              const isOwner = data.body.data.emailOwner === currentUserLogin;
              setIsCurrentUserOwner(isOwner);

              // Store the campaign ID
              setSelectedCampaignId(emailId);

              // Set email data
              setEmailData({
                emailName: data.body.data.emailName || '',
                emailOwner: data.body.data.emailOwner || ''
              });

              // Map the email data to the UI
              if (data.body.data.emailData) {
                mapModulesToHTML(data.body.data.emailData);
              }

              console.log('Email loaded from URL path:', data.body.data);
            }
          }
        } catch (error) {
          console.error('Error loading email from URL path:', error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    // Load email if campaign ID is present in path
    loadEmailFromParams();
  }, [mapModulesToHTML, setSelectedCampaignId, setEmailData]);

  return (
    <ServiceHomepage isLoading={isLoading} isCurrentUserOwner={isCurrentUserOwner} />
  );
};

export default App;
