import { createRoot } from 'react-dom/client';
import { HashRouter } from 'react-router-dom';
import App from './components/App';
import '@amzn/awsui-global-styles/polaris.css';
import { harmonyAPIGatewayAccessRole, CampaignsApiEndpoint, CampaignsApiRegion } from './config';

window.harmony.authorization.registerApi({
  url: CampaignsApiEndpoint,
  roleArn: harmonyAPIGatewayAccessRole,
  region: CampaignsApiRegion,
});
createRoot(document.querySelector('#root')!).render(
  <HashRouter>
    <App />
  </HashRouter>
);
