import { Module, Template, EmailData } from '../interfaces/email';
import { VALIDATION } from '../config/constants';

/**
 * Validates a TRK code
 * @param trk - The TRK code to validate
 * @returns boolean indicating if the TRK code is valid
 */
export const isValidTrk = (trk: string): boolean => {
  return VALIDATION.TRK_REGEX.test(trk) || trk === '';
};

/**
 * Validates a file name
 * @param fileName - The file name to validate
 * @returns boolean indicating if the file name is valid
 */
export const isValidFileName = (fileName: string): boolean => {
  return (
    fileName.trim().length > 0 &&
    fileName.length <= VALIDATION.MAX_FILE_NAME_LENGTH &&
    VALIDATION.FILE_NAME_PATTERN.test(fileName)
  );
};

/**
 * Formats email information for display
 * @param emailData - The email data to format
 * @returns Array of key-value pairs for display
 */
  // Add this function to format the email data for display
//  export const formatEmailInfoData = (data: any) => {
//     if (!data || !data.data) return [];

//     const emailData = data.data;
//     const emailVariant = emailData.emailData?.metadata?.variant || "-";

//     // Format the variant display value
//     let displayVariant = emailVariant;
//     if (variant.label === "audience segment" && variant.value) {
//       displayVariant = variant.value;
//     }

//     const items = [
//       { label: "Campaign ID", value: emailData.campaignId || "-" },
//       { label: "Email Owner", value: emailData.emailOwner || "-" },
//       { label: "Email Name", value: emailData.emailName || "-" },
//       { label: "Email Type", value: emailData.emailData?.metadata?.OPER ? "OPER" : "Marketing" },
//       { label: "Created At", value: emailData.createdAt ? new Date(emailData.createdAt).toLocaleString() : "-" },
//       { label: "Updated At", value: emailData.updatedAt ? new Date(emailData.updatedAt).toLocaleString() : "-" },
//       { label: "Variant", value: emailData.emailData?.metadata?.variant || "-" },
//       { label: "Modules Count", value: emailData.emailData?.modules?.length.toString() || "0" }
//     ];

//     return items;
//   };

/**
 * Generates a shareable URL for an email campaign
 * @param campaignId - The campaign ID
 * @returns The shareable URL
 */
export const generateShareableUrl = (campaignId: string): string => {
  const baseUrl = `${window.location.origin}/maverick`;
  return `${baseUrl}/${encodeURIComponent(campaignId)}`;
};

/**
 * Formats a campaign ID consistently
 * @param owner - The campaign owner
 * @param name - The campaign name
 * @param randomString - A random string for uniqueness
 * @returns The formatted campaign ID
 */
export const formatCampaignId = (owner: string, name: string, randomString: string): string => {
  const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '-');
  return `${owner}_${cleanName}_${randomString}`;
};

/**
 * Toggles a class on an element by ID
 * @param elementId - The element ID
 * @param className - The class to toggle
 * @param isChecked - Whether to add or remove the class
 */
export const toggleClassById = (elementId: string, className: string, isChecked: boolean): void => {
  const element = document.getElementById(elementId);
  if (element) {
    if (isChecked) {
      element.classList.add(className);
    } else {
      element.classList.remove(className);
    }
  }
};

/**
 * Prepares email data for API submission
 * @param campaignId - The campaign ID
 * @param data - The basic email data
 * @param metadata - The email metadata
 * @param elements - The email elements
 * @returns The prepared email data
 */
export const prepareEmailData = (campaignId: string, data: { emailName: string; emailOwner: string }, metadata: Metadata, elements: Module[]): EmailData => {
  return {
    campaignId,
    emailName: data.emailName.trim(),
    emailOwner: data.emailOwner.trim(),
    emailData: {
      metadata,
      modules: elements
                .filter((element) => element.selected)
                .map((element, index) => ({
                  id: element.id,
                  order: index,
                  copy: element.copy || 0,
                  module_type: element.label,
                  inputs: [...element.inputs],
                }))
    }
  };
};