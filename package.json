{"name": "@amzn/maverick", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "start": "npm run dev", "test": "vitest", "build-harmony-app": "build-harmony --templateName react", "publish": "build-harmony --templateName react --brazil-build"}, "dependencies": {"@amzn/awsui-collection-hooks": "^1.0.121", "@amzn/awsui-components-react": "^3.0.1635", "@amzn/awsui-design-tokens": "^3.0.216", "@amzn/awsui-global-styles": "^1.0.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@types/react": "^18.0.8", "@types/react-dom": "^18.0.3", "@types/react-router-dom": "^5.3.3", "html-react-parser": "^5.2.2", "immer": "^10.1.1", "nanoid": "^5.0.9", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-quill-new": "^3.3.3", "react-router-dom": "^6.3.0", "sass": "^1.89.0", "tslib": "^2.4.0", "zustand": "^5.0.2"}, "devDependencies": {"@amzn/harmony-types": "^1.0.36", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.0.0", "@types/jest": "^29.5.3", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.3.2", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "jsdom": "^22.1.0", "typescript": "^5.6.2", "vite": "^5.4.8", "vitest": "^2.1.1"}, "harmony": {"appName": "maverick", "jobCategories": [], "headers": [{"name": "Content-Security-Policy", "value": "connect-src u6vlwj5lth.execute-api.eu-west-2.amazonaws.com; img-src pages.awscloud.com; font-src pages.awscloud.com"}]}, "harmony-version": "1.7.172", "homepage": "/maverick/", "displayName": "Ma<PERSON><PERSON>", "description": "Probably the coolest app you've used in a while", "bindleId": "amzn1.bindle.resource.d2g5egwlmbbll4ncdveq"}